<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { Platform, PayType } from '@/common/env';
import { makeToast, ignoreRepeatAction } from '@/common/utils';
import { webClose } from '@/common/core';
import { MCProtocol } from '@simplex/simple-base';
import ReadProtocol from '@/components/ReadProtocol/index.vue';

// 导入图片资源
import backIcon from './images/1f8747.png';
import giftIcon from './images/5aacd0.png';
import videoEffectIcon from './images/e85009.png';
import quickGenIcon from './images/872fd9.png';
import oneClickIcon from './images/52d36a.png';
import hdQualityIcon from './images/af60c1.png';
import { checkedLogin } from '@/common/login';
import { appOrderPay, weixinMiniProgramPay } from '@/common/pay';
import readProtocolStore from '@/store/modules/readProtocol';
import { PayStatusEnum } from '@/api/pay';
import payTypeStore from '@/store/modules/payType';
import { getGroupSessionInfo, GoodsInfo, GroupKey } from '@/api/goods';
import { createGiftVipOrder } from '@/api/avtivity';

// 订阅选项数据
const goodsList = ref<GoodsInfo[]>([]);

const goodsInfo = ref<GoodsInfo>();

// 会员特权数据
const memberBenefits = ref([
  { icon: videoEffectIcon, title: '视频特效' },
  { icon: quickGenIcon, title: '快速生成' },
  { icon: oneClickIcon, title: '一键同款' },
  { icon: hdQualityIcon, title: '高清画质' }
]);

// 会员状态
const isVipMember = ref(false);
const memberInfo = ref({
  purchaseTime: '2025-02-01 12:20',
  expireTime: '2025-06-12',
  giftAmount: '300'
});

const payTypeInfo = payTypeStore();

const onPayTypeChange = (payType: PayType) => {
  payTypeInfo.setPayType(payType)
}

// 处理购买
async function handlePurchase() {
  // 判断协议是否勾选
  await checkReadProtocol();

  // 去登录
  await checkedLogin(false);

  // 下单并支付
  await createOrderAndPay();
}

// 恢复购买
async function handleRestorePurchase() {
}

const createOrderAndPay = ignoreRepeatAction(async function () {
  // TODO: 创建订单
  const res = await createGiftVipOrder({
    cardCode: 'valentine',
    words: '逢考必过',
    friendPhone: '15172387200',

    goodsName: goodsInfo.value!.name,
    goodsDataCode: goodsInfo.value!.dataCode,
    goodsDataType: goodsInfo.value!.dataType,
    channelCode: goodsInfo.value!.groupKey,
    appleGoodsId: goodsInfo.value!.appleId,
    activityType: goodsInfo.value!.activityType,
    activityCode: goodsInfo.value!.activityCode,
  });
  console.log(333, res);

  function goResultPage() {
    makeToast('购买成功！');
    isVipMember.value = true;
  }

  if (Platform.isInApp) {
    if (Platform.isIOS) {
      // @ts-ignore
      const data = await promisify(MCProtocol.Vip.makeApplePayment)({
        appleId: goodsInfo.value!.appleId,
        content: res.content,
        orderNumber: res.orderNumber,
        serviceName: 'vip'
      });
      console.log(444, data);
      if (data.success && data.data.status === 'success') {
        goResultPage();
      }
    } else {
      const payResult = await appOrderPay({
        content: res.content,
        orderNo: res.orderNumber
      });

      if (payResult.type === PayStatusEnum.PAY_SUCCESS) {
        goResultPage();
      }
    }
  } else if (Platform.isWeixinMiniProgram){
    weixinMiniProgramPay({
      content: res.content,
      orderNo: res.orderNumber
    }, {
      redirectUrl: location.href
    });
  }
});

const dialogVisible = ref(false);
let dialogResolve = (_confirm: boolean) => { };
function checkReadProtocol() {
  if (readProtocolStore().hasReadInfo) {
    return Promise.resolve();
  }
  return new Promise<void>((resolve, reject) => {
    dialogResolve = (confirm: boolean) => {
      if (confirm) {
        readProtocolStore().setHasRead(true);
        resolve();
      } else {
        reject();
      }
    };
    dialogVisible.value = true;
  });
}

// 处理去使用
const handleGoUse = () => {
  // 跳转到使用页面或关闭当前页面
  webClose();
};

async function fetchMemberInfo() {

}

async function fetchGoodsList() {
  const res = await getGroupSessionInfo({
    groupKeys: [
      'channel_kemuall_gift_card' as GroupKey, /** 全科超级VIP  */
      'channel_ke1ke4_group_gift_card' as GroupKey, /** 科一科四VIP */
      'channel_ke2ke3_group_gift_card' as GroupKey, /** 科二科三VIP */
    ]
  });
  goodsList.value = res;
  goodsInfo.value = goodsList.value[0];
}

onMounted(async () => {
  fetchMemberInfo();
  fetchGoodsList();
});
</script>

<template>
  <div class="vip-page">
    <!-- 可滚动内容区域 -->
    <div class="scrollable-content">
      <!-- 背景图片 -->
      <div class="background-image"></div>

      <!-- 顶部区域 -->
      <div class="header-section">
        <img :src="backIcon" alt="返回" class="back-button" />

        <!-- 渐变遮罩 -->
        <div class="gradient-overlay"></div>

        <!-- 标题区域 -->
        <div class="title-section">
          <h1 class="main-title">轻松开启动态创作之旅</h1>
          <p class="subtitle">视频特效</p>
        </div>
      </div>

      <!-- 会员标题 -->
      <div class="member-title-section">
        <h2 class="member-title">{{ isVipMember ? '已成为会员' : '成为会员' }}</h2>
        <p v-if="!isVipMember" class="member-subtitle">海量素材 超多功能快尝试</p>
        <div v-if="isVipMember" class="member-info">
          <p class="member-purchase-time">{{ memberInfo.purchaseTime }} 赠送{{ memberInfo.giftAmount }}<img
              :src="giftIcon" alt="赠送" class="gift-icon" /></p>
          <p class="member-expire-time">{{ memberInfo.expireTime }}到期，赠送的<img :src="giftIcon" alt="赠送"
              class="gift-icon" />未使用将过期;</p>
        </div>
      </div>

      <!-- 订阅选项 - 仅购买前显示 -->
      <div v-if="!isVipMember" class="subscription-section">
        <div class="subscription-options">
          <div v-for="(item, i) in goodsList" :key="i" class="subscription-option" :class="{
            'selected': goodsInfo === item
          }" @click="goodsInfo = item">
            <div class="option-title">{{ item.name }}</div>
            <div class="option-price">
              <span class="currency">¥ </span>
              <span class="amount">{{ item.payPrice }}</span>
            </div>
            <div class="original-price">¥{{ item.originalPrice }}</div>
            <div v-if="i === 0" class="popular-badge">限时体验</div>
            <div class="daily-price" v-else>
              ¥{{ +item.payPrice / 100 }}/天
            </div>
          </div>
        </div>

        <!-- 赠送说明 -->
        <div class="gift-info">
          <span class="gift-text">赠送300</span>
          <img :src="giftIcon" alt="赠送" class="gift-icon" />
          <span class="gift-desc">，到期后按28/周自动续费，可随时取消</span>
        </div>
      </div>

      <!-- 会员特权 -->
      <div class="benefits-section">
        <h3 class="benefits-title">尊享以下会员特权</h3>
        <div class="benefits-grid">
          <div v-for="benefit in memberBenefits" :key="benefit.title" class="benefit-item">
            <img :src="benefit.icon" :alt="benefit.title" class="benefit-icon" />
            <span class="benefit-title">{{ benefit.title }}</span>
          </div>
        </div>
      </div>

      <!-- 底部间距，为悬浮区域留出空间 -->
      <div class="bottom-spacer"></div>
    </div>

    <!-- 悬浮底部区域 -->
    <div class="floating-bottom">
      <!-- 底部渐变遮罩 -->
      <div class="bottom-gradient"></div>

      <!-- 购买按钮 -->
      <div class="purchase-section">
        <template v-if="!isVipMember">
          <!-- 支付方式选择 -->
          <div class="payment-methods" v-if="!(Platform.isInApp && Platform.isIOS)">
            <div v-for="item in payTypeInfo.payTypeList" class="payment-method" @click="onPayTypeChange(item.payType)"
              :class="{ 'selected': payTypeInfo.payType === item.payType }">
              <div class="payment-icon" :class="{
                'hwpay-icon': item.payType === PayType.Harmony,
                'wxpay-icon': item.payType === PayType.Weixin,
                'alipay-icon': item.payType === PayType.Alipay
              }"></div>
              <span class="payment-name">{{ item.text }}</span>
            </div>
          </div>
          <div class="purchase-button" @click="handlePurchase">
            <span class="price-text">¥{{ goodsInfo?.payPrice }}</span>
            <span class="purchase-text">立即购买</span>
          </div>
          <div class="agreement-section" @click="readProtocolStore().setHasRead(!readProtocolStore().hasReadInfo)">
            <div class="checkbox" :class="{ checked: readProtocolStore().hasReadInfo }"></div>
            <span class="agreement-text">开通前请阅读《会员协议》(含自动续贸条款) </span>
            <span class="restore-text" @click.stop="handleRestorePurchase">恢复购买></span>
          </div>
        </template>

        <div v-else class="use-button" @click="handleGoUse">
          <span class="use-text">去使用</span>
        </div>
      </div>
    </div>
  </div>

  <div class="dialog" v-if="dialogVisible">
    <div class="content">
      <ReadProtocol class="protocol" theme="1" :hideCheckbox="true" protocolText1="支付前请阅读" protocolText2="《会员协议》">
      </ReadProtocol>
      <div class="confirm-btn" @click="dialogVisible = false; dialogResolve(true)">同意并继续</div>
      <div class="cancel-btn" @click="dialogVisible = false; dialogResolve(false)">不同意</div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.vip-page {
  height: 100vh;
  background: #0a0a0a;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  width: 375px;
  margin: 0 auto;
}

.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 375px;
  height: 296px;
  background: lightblue;
}

.scrollable-content {
  flex: 1;
  overflow-y: auto;
  position: relative;
}

.header-section {
  position: relative;
  height: 296px;

  .back-button {
    position: fixed;
    top: 54px;
    left: 15px;
    width: 25px;
    height: 25px;
  }

  .gradient-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 130px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  }

  .title-section {
    position: absolute;
    bottom: 60px;
    left: 28px;
  }

  .main-title {
    color: white;
    font-size: 25px;
    font-weight: bold;
    line-height: 35px;
    margin: 0 0 5px 0;
  }

  .subtitle {
    color: rgba(255, 255, 255, 0.76);
    font-size: 15px;
    line-height: 21px;
    margin: 0;
  }
}

.member-title-section {
  text-align: center;
  margin: 20px 0;
  position: relative;

  .member-title {
    color: white;
    font-size: 25px;
    font-weight: bold;
    line-height: 36px;
    margin: 0 0 5px 0;
  }

  .member-subtitle {
    color: #6d6d6d;
    font-size: 15px;
    font-weight: bold;
    line-height: 21px;
    margin: 0;
  }

  .member-info {
    margin-top: 10px;
  }

  .member-purchase-time {
    color: white;
    font-size: 12px;
    line-height: 17px;
  }

  .member-expire-time {
    color: white;
    font-size: 12px;
    line-height: 17px;
  }
}

.subscription-section {
  padding: 0 19px;
  position: relative;

  .subscription-options {
    display: flex;
    gap: 11px;
    margin-bottom: 20px;
  }
}

.subscription-option {
  flex: 1;
  height: 125px;
  border-radius: 22px;
  padding-top: 15px;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #191a1f;

  &.selected {
    background: linear-gradient(140deg, #006488 11%, #14305d 41%, #1d1f2b 84%);
    box-shadow: inset 0 0 0 1.5px hsla(184, 100%, 54%, 1);
  }

  .option-title {
    color: white;
    font-size: 14px;
    font-weight: bold;
    line-height: 20px;
    text-align: center;
    margin-bottom: 10px;
  }

  .option-price {
    text-align: center;
    margin-bottom: 5px;
  }

  .currency {
    color: white;
    font-size: 16px;
    font-weight: bold;
    vertical-align: middle;
  }

  .amount {
    color: white;
    font-size: 26px;
    font-weight: bold;
  }

  .original-price {
    color: rgba(255, 255, 255, 0.35);
    font-size: 11px;
    line-height: 16px;
    text-decoration: line-through;
    text-align: center;
    margin-bottom: 5px;
  }

  .daily-price {
    color: rgba(255, 255, 255, 0.35);
    font-size: 11px;
    line-height: 16px;
    text-align: center;
  }

  .popular-badge {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 30px;
    background: linear-gradient(113deg, #6fbcff 3%, #4993e6 17%, #e876ff 86%, #f370ff 81%);
    color: white;
    font-size: 14px;
    line-height: 30px;
    text-align: center;
  }
}

.gift-info {
  margin-bottom: 30px;
  text-align: center;

  .gift-icon {
    width: 20px;
    height: 20px;
    vertical-align: bottom;
  }

  .gift-text {
    color: white;
    font-size: 12px;
    line-height: 17px;
  }

  .gift-desc {
    color: white;
    font-size: 12px;
    line-height: 17px;
  }
}

.benefits-section {
  text-align: center;
  margin-bottom: 30px;
  position: relative;

  .benefits-title {
    color: white;
    font-size: 20px;
    font-weight: bold;
    line-height: 28px;
    margin: 0 0 25px 0;
  }

  .benefits-grid {
    display: flex;
    justify-content: space-around;
    padding: 0 42px;
  }

  .benefit-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 6px;
  }

  .benefit-icon {
    width: 45px;
    height: 45px;
  }

  .benefit-title {
    color: white;
    font-size: 12px;
    font-weight: bold;
    line-height: 17px;
  }

  .bottom-spacer {
    height: 20px;
  }
}

.floating-bottom {
  position: relative;

  .bottom-gradient {
    position: absolute;
    top: -20px;
    left: 0;
    width: 100%;
    height: 130px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.93) 100%);
  }
}

.purchase-section {
  position: relative;
  padding: 0 16px;

  .payment-methods {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 14px;

    .payment-method {
      width: 144px;
      height: 32px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
      cursor: pointer;

      &.selected {
        background: hsla(0, 0%, 100%, 0.13);
      }
    }

    .payment-icon {
      width: 20px;
      height: 20px;
      border-radius: 100%;

      &.alipay-icon {
        background: url('@/assets/images/alipay.png') no-repeat;
        background-size: 100% 100%;
      }

      &.wxpay-icon {
        background: url('@/assets/images/wxpay.png') no-repeat;
        background-size: 100% 100%;
      }
    }

    .payment-name {
      color: hsla(0, 0%, 100%, 1);
      font-size: 14px;
      line-height: 20px;
    }
  }

  .purchase-button {
    position: relative;
    background: linear-gradient(305deg, rgba(223, 126, 236, 0.00) 27%, #df7eec 88%), linear-gradient(135deg, #2c89fe 1%, #2e6dff);
    border-radius: 10px;
    box-shadow: 0px 4px 12px -4px rgba(21, 22, 22, 0.50);
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 8px;
  }

  .price-text {
    color: white;
    font-size: 17px;
    line-height: 24px;
  }

  .purchase-text {
    color: white;
    font-size: 17px;
    line-height: 24px;
  }

  .use-button {
    background: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%);
    border-radius: 10px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0px 4px 12px -4px rgba(20, 20, 20, 0.5);
    margin-bottom: 15px;
    cursor: pointer;
  }

  .use-text {
    color: white;
    font-size: 17px;
    font-weight: bold;
    line-height: 24px;
  }

  .agreement-section {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 0 6px;
    margin-bottom: 15px;
  }

  .checkbox {
    width: 15px;
    height: 15px;
    border: 1.5px solid #676767;
    border-radius: 50%;
    flex-shrink: 0;
    margin-bottom: 2px;

    &.checked {
      background: #2E6EFF;
      border-color: #2E6EFF;
    }
  }

  .agreement-text {
    color: #A0A0A0;
    font-size: 12px;
    line-height: 17px;
    flex: 1;
  }

  .restore-text {
    color: #2E6EFF;
    font-size: 12px;
    line-height: 17px;
  }
}

.dialog {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.65);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1;

  .content {
    width: 320px;
    background: #ffffff;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 24px;

    .protocol {
      font-size: 15px;
      line-height: 21px;
    }

    .confirm-btn {
      margin-top: 24px;
      width: 230px;
      height: 40px;
      background: linear-gradient(146deg, #43caff 0%, #2ab2ff 100%);
      border-radius: 220px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      color: #ffffff;
    }

    .cancel-btn {
      margin-top: 2px;
      margin-bottom: 10px;
      padding: 10px;
      font-size: 13px;
      color: #a0a0a0;
      line-height: 18px;
    }
  }
}
</style>
