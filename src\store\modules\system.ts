import { computed, ref } from "vue";
import { defineStore } from "pinia";
import { MCProtocol } from '@simplex/simple-base'


const systemStore = defineStore("system_info", () => {
    const systemInfo = ref();
    
    function setSystemInfo(data: any) {
        systemInfo.value = data;
    }

    MCProtocol.Core.System.env((data: any) => {
        systemInfo.value = data.data
    });

    return { systemInfo, setSystemInfo };
});

export default systemStore;