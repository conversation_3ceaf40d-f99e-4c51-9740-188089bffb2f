import { request } from "../common/request"

const loginParams: any = {
    _platform: 'web',
    _authVersion: 1.5,
}

// base会拼接基础参数，如果业务参数带了这个会导致服务端取值不对（服务端的问题，这个参数属于业务参数，不应该用基础参数去判断）
if (!(JSON.parse(localStorage.getItem('MC_G_ABP') || '{}')['_appName'])) {
    loginParams['_appName'] = 'jiakaobaodian';
}

export function codeCheck(params: {
    phoneNumber: string,
    NECaptchaValidate: string,
}) {
    return request({
        hostName: 'login',
        method: 'POST',
        url: '/api/web/v3/login-sms/check.htm',
        data: {
            ...loginParams,
            ...params
        }
    })
}

export function sendLogin(params: {
    smsCode: string,
    smsId: string,
    phoneNumber: string
}): Promise<{
    authToken: string
}> {
    return request({
        hostName: 'login',
        url: '/api/web/v3/login-sms/login.htm',
        method: 'POST',
        data: {
            ...loginParams,
            ...params
        }
    })
}