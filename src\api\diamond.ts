import { request } from "@/common/request";

// 钻石明细数据类型
export interface DiamondRecord {
  id: string;
  type: 'consume' | 'recharge' | 'gift';
  title: string;
  amount: number;
  time: string;
  description?: string;
}

// 钻石明细查询参数
export interface DiamondRecordsParams {
  page?: number;
  pageSize?: number;
  type?: 'consume' | 'recharge' | 'gift' | 'all';
  startTime?: string;
  endTime?: string;
}

// 钻石明细响应数据
export interface DiamondRecordsResponse {
  records: DiamondRecord[];
  total: number;
  currentBalance: number;
}

/**
 * 获取钻石明细列表
 */
export function getDiamondRecords(params: DiamondRecordsParams = {}): Promise<DiamondRecordsResponse> {
  return request({
    hostName: 'api',
    url: 'api/diamond/records.htm',
    data: {
      page: 1,
      pageSize: 20,
      type: 'all',
      ...params
    }
  });
}

/**
 * 获取当前钻石余额
 */
export function getDiamondBalance(): Promise<{ balance: number }> {
  return request({
    hostName: 'api',
    url: 'api/diamond/balance.htm'
  });
}
