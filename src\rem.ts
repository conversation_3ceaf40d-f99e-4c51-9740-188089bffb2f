/** 设置页面基础宽度 */
const setBaseWidth = () => {
    const resizeEvt = 'orientationchange' in window ? 'orientationchange' : 'resize';

    const reCalc = function () {
        const dimensionWidth = Math.min(document.documentElement.clientWidth, 480);
        const baseFontSize = 100 * (dimensionWidth / 375);

        document.documentElement.style.fontSize = baseFontSize + 'px';
    };

    reCalc();
    window.addEventListener(resizeEvt, reCalc, false);
    document.addEventListener('DOMContentLoaded', reCalc, false);
};

setBaseWidth()