import { MCProtocol } from '@simplex/simple-base';

MCProtocol.register('Pay.channels', function (config) {
    return {
        callback: function (data) {
            if (typeof data === 'object') {
                config.callback((data));
            } else if (typeof data === 'string') {
                config.callback(JSON.parse(data));
            }
        }
    };
});

MCProtocol.register('Vip.makeApplePayment', function (config) {
    return config;
});