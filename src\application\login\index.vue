<script setup lang="ts">
import { useRouter } from 'vue-router';
import userStore from '@/store/modules/user';
import Login from '@/components/Login/index.vue';
import { LoginType, notifyLogin } from '@/common/broadcast/login';

const userInfo = userStore();

const router = useRouter();

const loginSuccess = (info: any) => {
    userInfo.setUserInfo(info);
    notifyLogin(LoginType.success, info.authToken)
    router.back();
};
</script>

<template>
    <div class="page-container page-login">
        <Login @loginSuccess="loginSuccess" />
    </div>
</template>

<style lang="less" scoped>
.page-login {
    background-color: #fff;
}
</style>
