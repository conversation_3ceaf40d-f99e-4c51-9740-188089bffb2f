<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { webClose } from '@/common/core';
import { makeToast } from '@/common/utils';
import { getDiamondRecords, type DiamondRecord } from '@/api/diamond';
import backIcon from './images/223eb6.png';

// 页面状态
const loading = ref(false);
const hasMore = ref(true);
const currentPage = ref(1);
const pageSize = 20;

// 钻石明细数据
const diamondRecords = ref<DiamondRecord[]>([]);

// 模拟数据（开发阶段使用）
const mockData: DiamondRecord[] = [
    {
        id: '1',
        type: 'consume',
        title: '修复老照片消耗',
        amount: -20.00,
        time: '2025-03-14  11:01:34'
    },
    {
        id: '2',
        type: 'consume',
        title: '照片动起来消耗',
        amount: -10.00,
        time: '2025-03-14  11:01:34'
    },
    {
        id: '3',
        type: 'recharge',
        title: '钻石充值',
        amount: 300.00,
        time: '2025-03-14  11:01:34'
    },
    {
        id: '4',
        type: 'gift',
        title: '新用户赠送',
        amount: 50.00,
        time: '2025-03-14  11:01:34'
    }
];

// 返回按钮
const handleBack = () => {
    webClose();
};

// 获取金额颜色类名
const getAmountColorClass = (type: string) => {
    switch (type) {
        case 'consume':
            return 'amount-consume';
        case 'recharge':
        case 'gift':
            return 'amount-income';
        default:
            return '';
    }
};

// 格式化金额显示
const formatAmount = (amount: number) => {
    const sign = amount >= 0 ? '+' : '';
    return `${sign}${amount.toFixed(2)}`;
};

// 加载钻石明细数据
const loadDiamondRecords = async (isRefresh = false) => {
    if (loading.value) return;

    try {
        loading.value = true;

        if (isRefresh) {
            currentPage.value = 1;
            diamondRecords.value = [];
            hasMore.value = true;
        }

        // 在实际项目中，这里应该调用真实的API
        // const response = await getDiamondRecords({
        //   page: currentPage.value,
        //   pageSize: pageSize
        // });

        // 模拟API调用延迟
        await new Promise(resolve => setTimeout(resolve, 500));

        // 使用模拟数据
        const newRecords = mockData.map((item, index) => ({
            ...item,
            id: `${currentPage.value}_${index}`,
            time: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            }).replace(/\//g, '-')
        }));

        if (isRefresh) {
            diamondRecords.value = newRecords;
        } else {
            diamondRecords.value.push(...newRecords);
        }

        currentPage.value++;

        // 模拟没有更多数据的情况
        if (currentPage.value > 3) {
            hasMore.value = false;
        }

    } catch (error) {
        console.error('加载钻石明细失败:', error);
        makeToast('加载失败，请重试');
    } finally {
        loading.value = false;
    }
};

// 下拉刷新
const handleRefresh = () => {
    loadDiamondRecords(true);
};

// 加载更多
const handleLoadMore = () => {
    if (hasMore.value && !loading.value) {
        loadDiamondRecords();
    }
};

onMounted(() => {
    loadDiamondRecords(true);
});
</script>

<template>
    <div class="diamond-details-page">
        <!-- 导航栏 -->
        <div class="navigation">
            <img :src="backIcon" alt="返回" class="nav-icon" @click="handleBack" />
            <div class="nav-title">钻石明细</div>
            <div class="nav-placeholder"></div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 刷新提示 -->
            <div class="refresh-hint" @click="handleRefresh">
                <span>下拉刷新</span>
            </div>

            <!-- 钻石明细列表 -->
            <div class="records-list" v-if="diamondRecords.length > 0">
                <div v-for="record in diamondRecords" :key="record.id" class="record-item">
                    <div class="record-info">
                        <div class="record-title">{{ record.title }}</div>
                        <div class="record-time">{{ record.time }}</div>
                    </div>
                    <div class="record-amount" :class="getAmountColorClass(record.type)">
                        {{ formatAmount(record.amount) }}
                    </div>
                </div>
            </div>

            <!-- 空状态 -->
            <div class="empty-state" v-else-if="!loading">
                <div class="empty-icon">💎</div>
                <div class="empty-text">暂无钻石明细记录</div>
                <div class="empty-desc">您的钻石使用记录将在这里显示</div>
            </div>

            <!-- 加载状态 -->
            <div class="loading-state" v-if="loading">
                <div class="loading-spinner"></div>
                <div class="loading-text">加载中...</div>
            </div>

            <!-- 加载更多 -->
            <div class="load-more" v-if="diamondRecords.length > 0 && hasMore && !loading" @click="handleLoadMore">
                <span>加载更多</span>
            </div>

            <!-- 没有更多数据 -->
            <div class="no-more" v-if="diamondRecords.length > 0 && !hasMore">
                <span>没有更多数据了</span>
            </div>
        </div>
    </div>
</template>

<style lang="less" scoped>
.diamond-details-page {
    min-height: 100vh;
    background: hsla(0, 0%, 4%, 1);
    display: flex;
    flex-direction: column;
}

.navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    flex-shrink: 0;

    .nav-icon {
        width: 25px;
        height: 25px;
        cursor: pointer;
    }

    .nav-title {
        color: hsla(0, 0%, 100%, 1);
        font-size: 15px;
        line-height: 14px;
        font-weight: bold;
    }

    .nav-placeholder {
        width: 25px;
        height: 25px;
    }
}

.main-content {
    flex: 1;
    padding: 0 16px;
    overflow-y: auto;
}

.refresh-hint {
    text-align: center;
    padding: 10px;
    color: hsla(0, 0%, 63%, 1);
    font-size: 12px;
    cursor: pointer;

    &:active {
        opacity: 0.7;
    }
}

.records-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 20px;
}

.record-item {
    background: hsla(230, 11%, 11%, 1);
    border-radius: 12px;
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.2s ease;

    &:active {
        background: hsla(230, 11%, 13%, 1);
    }

    .record-info {
        flex: 1;
    }

    .record-title {
        color: hsla(0, 0%, 100%, 1);
        font-size: 16px;
        line-height: 22px;
        margin-bottom: 8px;
    }

    .record-time {
        color: hsla(0, 0%, 63%, 1);
        font-size: 12px;
        line-height: 17px;
    }

    .record-amount {
        font-size: 14px;
        line-height: 20px;
        font-weight: 500;

        &.amount-consume {
            color: hsla(3, 100%, 63%, 1);
        }

        &.amount-income {
            color: hsla(177, 100%, 39%, 1);
        }
    }
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;

    .empty-icon {
        font-size: 48px;
        margin-bottom: 16px;
        opacity: 0.6;
    }

    .empty-text {
        color: hsla(0, 0%, 100%, 1);
        font-size: 16px;
        line-height: 22px;
        margin-bottom: 8px;
    }

    .empty-desc {
        color: hsla(0, 0%, 63%, 1);
        font-size: 14px;
        line-height: 20px;
    }
}

.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;

    .loading-spinner {
        width: 24px;
        height: 24px;
        border: 2px solid hsla(0, 0%, 63%, 0.3);
        border-top: 2px solid hsla(0, 0%, 100%, 1);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 12px;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }

    .loading-text {
        color: hsla(0, 0%, 63%, 1);
        font-size: 14px;
        line-height: 20px;
    }
}

.load-more,
.no-more {
    text-align: center;
    padding: 20px;
    color: hsla(0, 0%, 63%, 1);
    font-size: 14px;
    line-height: 20px;
}

.load-more {
    cursor: pointer;

    &:active {
        opacity: 0.7;
    }
}
</style>