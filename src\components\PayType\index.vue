<script setup lang="ts">
import { PayType } from '@/common/env';
import payTypeStore from '@/store/modules/payType';

const payTypeInfo = payTypeStore();
const props = defineProps(['theme']);

const onPayTypeChange = (payType: PayType) => {
    payTypeInfo.setPayType(payType)
}
</script>
<template>
    <div :class="[`wrapper-${props.theme}`]">
        <div v-for="item in payTypeInfo.payTypeList" class="pay-item" @click="onPayTypeChange(item.payType)">
            <div
                class="pay-icon" 
                :class="{
                    'hwpay-icon': item.payType === PayType.Harmony,
                    'wxpay-icon': item.payType === PayType.Weixin,
                    'alipay-icon': item.payType === PayType.Alipay
                }"
            ></div>
            <div class="pay-desc">{{ item.text }}</div>
            <div class="xz-icon" :class="{active: payTypeInfo.payType === item.payType}"></div>
        </div>
    </div>
</template>

<style lang="less" scoped>
@import './index.less';
</style>
