<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue';

const emit = defineEmits<{
    (e: 'Ok', flag: boolean): void;
    (e: 'Close', flag: false | null): void;
}>();

const times = ref(10);
let timer: ReturnType<typeof setInterval>;

onMounted(() => {
    timer = setInterval(() => {
        times.value = times.value - 1;

        if (times.value <= 0) {
            clearInterval(timer);
            emit('Ok', true);
        }
    }, 1000);
});
onUnmounted(() => {
    clearInterval(timer);
});
</script>
<template>
    <div class="time-loading">
        <div class="time">{{ times }}</div>
        <div class="dec">确认支付状态中，请勿点击或退出APP</div>
    </div>
</template>

<style lang="less" scoped>
@import './index.less';
</style>
