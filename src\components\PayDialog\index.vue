<script setup lang="ts">
import { checkReadedDialog } from '@/common/dom-function';
import PayType from '@/components/PayType/index.vue';
import ReadProtocol from '@/components/ReadProtocol/index.vue';

const emit = defineEmits<{
    (e: 'Ok', flag: boolean): void;
    (e: 'Close', flag: false | null): void;
}>();
const props = defineProps(['fullScreen', 'title', 'protocolList', 'payPrice', 'coupon', 'dec', 'readProtocolTheme']);

const onClose = () => {
    emit('Close', null);
};

const onPayConfirm = () => {
    checkReadedDialog({
        theme: props.readProtocolTheme,
        protocolList: props.protocolList
    }).then(() => {
        emit('Ok', true);
    });
};
</script>
<template>
    <div class="modal" :style="{ 'background-color': `rgba(0, 0, 0, 0.5)` }">
        <div class="pay-content" :class="{ fullScreen: props.fullScreen }">
            <div class="pay-hd">
                <p @click="onClose" class="close-icon"></p>
                <div class="payTitle">{{ props.title || '选择支付方式' }}</div>
                <div class="pay-dec" v-if="props.dec" v-html="props.dec" ></div>
            </div>
            <div class="pay-body">
                <PayType />
            </div>
            <div class="pay-ft">
                <ReadProtocol :protocolList="props.protocolList" :theme="props.readProtocolTheme" />

                <div class="pay-confirm" @click="onPayConfirm">
                    确认支付
                    <template v-if="props.payPrice">
                        &nbsp;&nbsp;¥&nbsp;<span>{{ props.payPrice || '--' }}</span>
                    </template>

                    <template v-if="props.coupon?.priceCent">
                        <div class="tips">已优惠{{ props.coupon.priceCent }}元</div>
                    </template>
                </div>
            </div>
        </div>
    </div>
</template>

<style lang="less" scoped>
@import './index.less';
</style>
