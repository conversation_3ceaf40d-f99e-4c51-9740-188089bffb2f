import { CouponInfo, getUserCoupons } from "@/api/coupon";
import { GoodsInfo, GroupKey } from "@/api/goods";
import { formatPrice } from "./utils";

export interface Coupon {
    code: string;
    price: string;
    name: string
    /** 展示用 */
    hint?: string;
}


export function couponInfo2Coupon(couponInfo: CouponInfo): Coupon {
    return { code: couponInfo.couponCode || '', price: formatPrice(couponInfo.priceCent), name: couponInfo.goodsCouponData.name };
}

export async function getBestCoupon(goodsInfo: GoodsInfo): Promise<Coupon> {

    const { canUseCoupons } = await getUserCoupons({
        goodsDataType: goodsInfo.dataType,
        goodsDataCode: goodsInfo.dataCode
    });

    if (!canUseCoupons.length) {
        return {
            code: '',
            price: '',
            name: ''
        };
    }

    let maxPrice = 0;
    let maxIndex = 0;
    for (let i = 0; i < canUseCoupons.length; i++) {
        const iData = canUseCoupons[i];
        if (iData.priceCent > maxPrice) {
            maxPrice = iData.priceCent;
            maxIndex = i;
        }
    }
    const bestCoupon = canUseCoupons[maxIndex];

    return couponInfo2Coupon(bestCoupon);
}


/** 批量获取用户最佳优惠券 */
export async function batchGetBestCoupon(goodsList: GoodsInfo[]) {
    const couponList = await Promise.all(goodsList.map((goods) => getBestCoupon(goods)));

    return couponList.reduce<Partial<Record<GroupKey, Coupon>>>((couponPool, coupon, i) => {
        const goods = goodsList[i];
        couponPool[goods.groupKey] = coupon;
        return couponPool;
    }, {});
}
