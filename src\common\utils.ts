import { MCGeo } from "@simplex/simple-base";
import { Platform } from "./env";

/** url签名 */
export function sign(a: number): string {
    const c = Math.abs(parseInt((new Date().getTime() * Math.random() * 10000) + '')).toString();
    let d: any = 0;
    for (let b = 0; b < c.length; b++) {
        d += parseInt(c[b]);
    }
    const e = ((f: any) => {
        return (g: string | any[], h: number) => {
            return ((h - 0 + g.length) <= 0) ? g : (f[h] || (f[h] = Array(h + 1).join('0'))) + g;
        };
    })([]);

    d += c.length;
    d = e(d, 3 - d.toString().length);
    return a.toString() + c + d;
}

/** 时间展示 */
export function dateFormat(dateValue: string | number | Date, formatStr = 'yyyy-MM-dd hh:mm', showDayType = 24) {
    let timestamp = new Date(dateValue);
    let fmt = formatStr;

    const week = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
    const o = {
        'M+': timestamp.getMonth() + 1,
        'd+': timestamp.getDate(),
        'h+': timestamp.getHours() % showDayType,
        'H+': timestamp.getHours(),
        'm+': timestamp.getMinutes(),
        's+': timestamp.getSeconds(),
        'q+': Math.floor((timestamp.getMonth() + 3) / 3),
        'S': timestamp.getMilliseconds(),
        'W+': week[timestamp.getDay()]
    };
    let k: keyof typeof o;
    if (!dateValue) {
        return '';
    }

    if (typeof timestamp !== 'object') {
        timestamp = new Date(timestamp);
    }
    fmt = fmt || 'yyyy-MM-dd';

    if ((/(y+)/).test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (timestamp.getFullYear() + '').substr(4 - RegExp.$1.length));
    }
    for (k in o) {
        if (new RegExp('(' + k + ')').test(fmt)) {
            // eslint-disable-next-line eqeqeq
            fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)) as any);
        }
    }
    return fmt;

}

/** 价格展示 */
export function formatPrice(price: number) {
    let fmtPic;
    if (price % 100 > 0) {
        if ((price % 100) % 10 > 0) {
            fmtPic = (price / 100).toFixed(2);
        } else {
            fmtPic = (price / 100).toFixed(1);
        }
    } else {
        fmtPic = (price / 100).toFixed(0);
    }
    return fmtPic;
}


export const deleteEmpty = (obj: any) => {
    for (const key in obj) {
        if (obj[key] === null || obj[key] === undefined) {
            delete obj[key];
        }
    }
    return obj;
};


/** 模拟toast */
export function makeToast(message: string, foz = '14px', time = 2000) {
    return new Promise((resolve) => {
        if (document.getElementById('myToast')) {
            document.body.removeChild(document.getElementById('myToast') as Node);
        }

        const div: HTMLDivElement = document.createElement('div');
        div.innerText = message;

        div.setAttribute('id', 'myToast');

        div.style.position = 'fixed';
        div.style.left = '50%';
        div.style.top = '50%';
        div.style.transform = 'translate(-50%, -50%)';
        div.style.webkitTransform = 'translate(-50%, -50%)';
        div.style.background = 'rgba(0, 0, 0, 0.7)';
        div.style.zIndex = '9999';
        div.style.padding = '5px 10px';
        div.style.borderRadius = '3px';
        div.style.textAlign = 'center';
        div.style.color = '#ffffff';
        div.style.maxWidth = '90%';
        // div.style.minWidth = '60%';
        div.style.fontSize = foz;
        div.style.lineHeight = '1.5';

        document.body.appendChild(div);
        setTimeout(function () {
            div.remove();
            resolve('');
        }, time);
    });
}

// 定义一个防抖函数类型
type DebounceFunction = (...args: any[]) => any;
// 防抖器函数
export function debounce<F extends DebounceFunction>(func: F, wait: number = 500): F {
    let timeout: ReturnType<typeof setTimeout> | null = null;

    // 返回一个新的函数，这个函数会在指定的延迟后执行原函数
    return function (...args: Parameters<F>) {
        // 如果已经设置过定时器，则清除定时器
        if (timeout !== null) {
            clearTimeout(timeout);
        }

        // 设置一个新的定时器，在指定的延迟后执行原函数
        timeout = setTimeout(() => {
            func.apply(this, args);
        }, wait);
    } as F;
}

export function copyTextToClipboard(text: string): void {
    if (!navigator.clipboard) {
        fallbackCopyTextToClipboard(text);
        return;
    }
    navigator.clipboard.writeText(text).then(() => {
        console.log('复制成功: ' + text);
        makeToast('复制成功');
    }).catch((err) => {
        makeToast('复制失败');
        console.error('复制失败', err);
    });
}

function fallbackCopyTextToClipboard(text: string): void {
    const textArea = document.createElement('textarea');
    textArea.value = text;

    // 防止在移动设备上弹出键盘
    textArea.setAttribute('readonly', '');
    textArea.style.position = 'absolute';
    textArea.style.left = '-9999px';

    document.body.appendChild(textArea);
    textArea.select();
    try {
        const successful = document.execCommand('copy');
        const msg = successful ? '复制成功' : '复制失败';
        console.log(msg);
        makeToast(msg);
    } catch (err) {
        console.error('无法复制', err);
    }

    document.body.removeChild(textArea);
}

export const formatNumber = (n: number) => {
    const s = n.toString();
    return s[1] ? s : '0' + s;
};

export function showClock(time: number, format: string = 'mm:ss') {
    var hh = formatNumber(Math.floor(time / 3600));
    var mm = formatNumber(Math.floor(Math.floor(time % 3600) / 60));
    var ss = formatNumber(Math.floor(time % 60));
    var str = '';
    if (format.indexOf('hh') > -1) {
        str += hh + ':'
    }
    if (format.indexOf('mm') > -1) {
        str += mm + ':'
    }
    if (format.indexOf('ss') > -1) {
        str += ss
    }
    return str;
}


/** 将木仓协议的调用转为promise形式 */
export function promisify(mcprotocolFn: AnyFunc = () => false) {
    return function (config?: any) {
        return new Promise<any>(resolve => mcprotocolFn({ ...config, callback: resolve }));
    };
}

/** 防重复执行 */
export const ignoreRepeatAction = <T extends (...args: any[]) => Promise<unknown>>(fn: T) => {
    let invoking = false;
    return function () {
        if (invoking) {
            return Promise.reject(new Error("防重复点击"));
        }
        invoking = true;
        return fn.apply(null, arguments as any).finally(() => {
            invoking = false;
        });
    } as unknown as T;
};

export const loadScript = (url: string) => {
    return new Promise<void>((resolve, reject) => {
        var $script = document.createElement("script");
        $script.onload = function () {
            document.body.removeChild($script)
            resolve();
        }
        $script.onerror = function (err) {
            reject(err);
        }
        $script.setAttribute("crossorigin", "");
        $script.src = url;
        document.body.appendChild($script)
    });
}

export const getLocateCityCode = () => {
    return new Promise<string>((resolve, reject) => {
        // @ts-ignore
        MCGeo.cfg = {
            force: true,
            gpsType: 'gcj02',
            // @ts-ignore
            change: false,
            success: (data: any) => {
                if (data.cityCode) {
                    console.log('定位到城市', data.cityCode);

                    resolve(data.cityCode.replace(/^(\d{4})\d{2}$/, '$100'));
                } else {
                    const { latitude, longitude } = data;
                    console.log('定位到经纬度', latitude, longitude);

                    var gaodeMapUrl = 'https://webapi.amap.com/maps?v=1.4.6&key=5d4e2bfebc376849b0fd27a963934135&plugin=AMap.Geocoder';
                    loadScript(gaodeMapUrl).then(function () {
                        (window as any).AMap.plugin('AMap.Geocoder', function () {
                            var geocoder = new (window as any).AMap.Geocoder({
                                // city 指定默认逆地理编码搜索的地区，可选值：cityname（中文或中文全拼）、adcode（中华人民共和国行政区划代码）
                                city: "全国"
                            });

                            // 经纬度坐标：lng（经度）, lat（纬度）
                            var lnglat = [longitude, latitude];
                            geocoder.getAddress(lnglat, function (status: any, result: any) {
                                if (status === 'complete' && result.regeocode) {
                                    console.log('geocoder.getAddress', result); // 输出详细地址信息
                                    resolve(String(result.regeocode.addressComponent.adcode).replace(/^(\d{4})\d{2}$/, '$100'));
                                } else {
                                    reject('根据经纬度查询地址失败');
                                }
                            });
                        });
                    });
                }
            },
            fail(error: any) {
                reject(error.message);
            },
        };
        if (Platform.isWeiXin) {
            MCGeo.getWxGeo();
        } else {
            // @ts-ignore
            MCGeo.getIpGeo();
        }
    });
}