// h5跟微信小程序交互
// h5要发消息时，改变页面的hash，小程序那边会监听url变化，然后接收到消息
// 小程序通过postMessage发消息给h5
// 消息格式会有id和内容，以json格式表示

import { Platform } from './env';

export function weappJSBridge() {
    const weappJSBridge = {
        send: function (message: any) {
            if (Platform.isWeixinMiniProgram) {
                window.location.hash = '?message=' + JSON.stringify(message);
            }
        },
        on: function (callback: (message: any) => void) {
            if (Platform.isWeixinMiniProgram) {
                window.addEventListener('message', function (event) {
                    callback(event.data);
                });
            }
        },
        promise: function (params: any) {
            return new Promise((resolve) => {
                weappJSBridge.send(params);
                weappJSBridge.on(resolve);
            });
        }
    };

    return weappJSBridge;
}
