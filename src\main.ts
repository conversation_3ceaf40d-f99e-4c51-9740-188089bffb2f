import Vconsole from "vconsole";
if (import.meta.env.MODE === "development") {
    // 测试和开发打开，生产不能打开
    new Vconsole();
}

import { createApp } from 'vue'
import './assets/init.less'
import './style.css'
import './rem.ts'
import '@/common/mcprotocol.ts'
import App from './App.vue'
import router from './router'
import pinia from './store'
import { MCBaseStore } from '@simplex/simple-base';

let host = {
    "activity": "https://jiakao-activity.kakamobi.cn/",
    "squirrel": "https://squirrel.kakamobi.cn/",
    "login": "https://auth.kakamobi.com",
    "apiShare": "https://api-share-m.kakamobi.com/",
};
let testHost = {
    "activity": "https://jiakao-activity.ttt.mucang.cn/",
    "squirrel": "https://squirrel.ttt.mucang.cn/",
    "login": "https://auth.kakamobi.com",
    "apiShare": "https://api-share-m.kakamobi.com/",
};

MCBaseStore.setHosts(import.meta.env.MODE === "development" ? testHost : host);

const app = createApp(App);
app.use(pinia)
app.use(router)
app.mount('#app')
