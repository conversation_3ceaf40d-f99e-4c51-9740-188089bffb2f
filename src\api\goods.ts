/*
 * ------------------------------------------------------------------
 * 商品相关接口
 * ------------------------------------------------------------------
 */
import { MCProtocol } from '@simplex/simple-base';
import { URLParams, URLCommon, CarType } from '@/common/env';
import { webClose } from '@/common/core';
import { request } from '@/common/request';
import { dateFormat, formatPrice } from '@/common/utils';

/** 商品的groupkey */
export enum GroupKey {
    /*
     | 驾驶证相关
     */

    /** 奖励优惠商品科一 */
    ExcellentChannelKe1 = 'channel_excellent_ke1',
    /** 奖励优惠商品科四 */
    ExcellentChannelKe4 = 'channel_excellent_ke4',
    /** 长辈版科一 */
    ElderChannelKe1 = 'channel_ke1_elder',
    /** 长辈版科四 */
    ElderChannelKe4 = 'channel_ke4_elder',
    /** 长辈版科一单次购买模拟考试 */
    ElderChannelKe1Alone = '',
    /** 长辈版科四单次购买模拟考试 */
    ElderChannelKe4Alone = '',

    /** 3d小车 */
    ChannelKe1D3 = 'channel_ke1_3d',
    ChannelKe4D3 = 'channel_ke4_1_3d',
    /** 3d客车 */
    KcChannelKe1D3 = 'channel_ke1_kc_3d',
    KcChannelKe4D3 = 'channel_ke4_kc_3d',
    /** 3d货车 */
    HcChannelKe1D3 = 'channel_ke1_hc_3d',
    HcChannelKe4D3 = 'channel_ke4_hc_3d',

    /** 小车科一 */
    ChannelKe1 = 'channel_ke1',
    /**  小车科四30天 */
    ChannelKe1Month = 'channel_ke1_month',
    /** 小车科二 */
    ChannelKe2 = 'channel_ke2',
    /**  小车科四30天 */
    ChannelKe2Month = 'channel_ke2_month',
    /** 小车科二升级 */
    ChannelKe2Asset = 'channel_ke2_asset',
    /**  小车科三 */
    ChannelKe3 = 'channel_ke3',
    /**  小车科三group包 */
    ChannelKe3Group = 'channel_ke3_group',
    /**  小车科三路线 */
    ChannelKe3Route = 'channel_ke3_route',
    /**  小车科三视频包 */
    ChannelKe3RouteMeta = 'channel_ke3_route_meta',
    /**  小车科三科四路线 */
    ChannelKe34Route = 'channel_route3dke4vip',
    /**  小车科三路线维语 */
    ChannelKe3RouteWeiyu = 'channel_ke3_route_weiyu',
    /** 小车科三科四 */
    ChannelKe34 = 'channel_ke3ke4vip',
    /**  小车科四 */
    ChannelKe4 = 'channel_ke4_1',
    /**  小车科四30天 */
    ChannelKe4Month = 'channel_ke4_month',
    /** 小车科一单次购买模拟考试 */
    ChannelKe1Alone = 'channel_ke1_exam',
    /** 小车科四单次购买模拟考试 */
    ChannelKe4Alone = 'channel_ke4_exam',
    /**  小车科四短时提分 */
    ChannelKe4Short = 'channel_ke4_2',
    /**  小车全科 */
    ChannelKemuAll = 'channel_kemuall_new',
    /**  小车全科学生活动 */
    ChannelKemuAllStudent = 'channel_kemuall_stud',
    /**  小车扣满12分三套卷 */
    ChannelKou12V1 = 'channel_kou12_1',
    /**  小车扣满12分 */
    ChannelKou12 = 'channel_kou12_2',
    /**  小车扣满12分短时提分 */
    ChannelKou12Short = 'channel_kou12_3',
    /**  小车扣满12分单次购买模拟考试 */
    ChannelKou12Alone = '',
    /** 小车科一多次购买模拟考试 */
    ChannelKe1ExamNum3 = 'channel_ke1_exam_num3',
    /** 小车科四多次购买模拟考试 */
    ChannelKe4ExamNum3 = 'channel_ke4_exam_num3',
    /** 小车科一考前辅导 */
    ChannelKe1Kqfd = 'channel_ke1_kqfd',
    /** 小车科一模拟考试 */
    ChannelKe1Kcmn = 'channel_ke1_kcmn',
    /** 小车科四模拟考试 */
    ChannelKe4Kcmn = 'channel_ke4_kcmn',

    /*
     | 小车短时促销
     */
    ChannelKe1Sale = 'channel_ke1_sale',
    ChannelKe4Sale = 'channel_ke4_sale',
    ChannelKemuAllSale = 'channel_kemuall_sale',

    /**  客车科一 */
    KcChannelKe1 = 'channel_ke1_kc',
    /**  客车科二 */
    KcChannelKe2 = 'channel_ke2_kc',
    /**  客车科三 */
    KcChannelKe3 = 'channel_ke3_kc',
    /** 客车科三科四 */
    KcChannelKe34 = 'channel_ke3ke4vip_kc',
    /**  客车科四 */
    KcChannelKe4 = 'channel_ke4_kc',
    /** 客车科一单次购买模拟考试 */
    KcChannelKe1Alone = '',
    /** 客车科四单次购买模拟考试 */
    KcChannelKe4Alone = '',
    /**  客车科一科四组合 */
    KcChannelKe1Ke4Group = 'channel_ke1ke4_group_kc',
    /**  客车科二科三组合 */
    KcChannelKe2Ke3Group = 'channel_ke2ke3_group_kc',
    /**  客车科一短时提分 */
    KcChannelKe1Short = 'channel_ke1_1_kc',
    /**  客车科四短时提分 */
    KcChannelKe4Short = 'channel_ke4_1_kc',
    /**  客车全科 */
    KcChannelKemuAll = 'channel_kemuall_kc',
    /**  客车扣满12分三套卷 */
    KcChannelKou12V1 = 'channel_kou12_1_kc',
    /**  客车扣满12分 */
    KcChannelKou12 = 'channel_kou12_2_kc',
    /**  客车扣满12分短时提分 */
    KcChannelKou12Short = 'channel_kou12_3_kc',
    /**  客车扣满12分单次购买模拟考试 */
    KcChannelKou12Alone = '',

    /**  货车科一 */
    HcChannelKe1 = 'channel_ke1_hc',
    /**  货车365天 */
    HcChannelKe1Time365 = 'channel_kemuall_hc_365',
    /**  货车科二 */
    HcChannelKe2 = 'channel_ke2_hc',
    /**  货车科三 */
    HcChannelKe3 = 'channel_ke3_hc',
    /** 货车科三科四 */
    HcChannelKe34 = 'channel_ke3ke4vip_hc',
    /**  货车科四 */
    HcChannelKe4 = 'channel_ke4_hc',
    /** 货车科一单次购买模拟考试 */
    HcChannelKe1Alone = 'channel_ke1_exam_hc',
    /** 货车科四单次购买模拟考试 */
    HcChannelKe4Alone = 'channel_ke4_exam_hc',
    /** 货车科一多次购买模拟考试 */
    HcChannelKe1ExamNum3 = 'channel_ke1_exam_num3_hc',
    /** 货车科四多次购买模拟考试 */
    HcChannelKe4ExamNum3 = 'channel_ke4_exam_num3_hc',
    /** 货车优惠科一 */
    HcChannelExcellentKe1 = 'channel_excellent_ke1_hc',
    /** 货车优惠科四 */
    HcChannelExcellentKe4 = 'channel_excellent_ke4_hc',
    /** 货车科一科四组合包*/
    HcChannelKe1Ke4Group = 'channel_ke1ke4_group_hc',
    /** 货车车科二科三组合 */
    HcChannelKe2Ke3Group = 'channel_ke2ke3_group_hc',
    /**  货车全科 */
    HcChannelKemuAll = 'channel_kemuall_hc',
    /**  货车扣满12分三套卷 */
    HcChannelKou12V1 = 'channel_kou12_1_hc',
    /**  货车扣满12分 */
    HcChannelKou12 = 'channel_kou12_2_hc',
    /**  货车科四短时提分 */
    HcChannelKe4Short = 'channel_ke4_1_hc',
    /**  货车扣满12分短时提分 */
    HcChannelKou12Short = 'channel_kou12_3_hc',
    /**  货车扣满12分单次购买模拟考试 */
    HcChannelKou12Alone = '',
    /**  货车科一模拟考试 */
    ChannelKe1Kcmnhc = 'channel_ke1_kcmn_hc',
    /**  货车科四模拟考试 */
    ChannelKe4Kcmnhc = 'channel_ke4_kcmn_hc',

    /**  摩托科一 */
    MotoChannelKe1 = 'channel_ke1_moto',
    /**  摩托科二 */
    MotoChannelKe2 = 'channel_ke2_moto',
    /**  摩托科三 */
    MotoChannelKe3 = 'channel_ke3_moto',
    /** 摩托车科三科四组合 */
    MotoChannelKe3ke4vip = 'channel_ke3ke4vip_moto',
    /**  摩托科四 */
    MotoChannelKe4 = 'channel_ke4_moto',
    /** 摩托科一单次购买模拟考试 */
    MotoChannelKe1Alone = 'channel_ke1_exam_moto',
    /** 摩托科四单次购买模拟考试 */
    MotoChannelKe4Alone = 'channel_ke4_exam_moto',
    /**   摩托扣满12分 */
    MotoChannelKou12 = 'channel_kou12_2_moto',
    /**  摩托扣满12分三套卷 */
    MotoChannelKou12V1 = 'channel_kou12_1_moto',
    /**  摩托扣满12分短时提分 */
    MotoChannelKou12Short = 'channel_kou12_3_moto',
    /**  摩托科一短时提分 */
    MotoChannelKe1Short = 'channel_ke1_1_moto',
    /**  摩托科四短时提分 */
    MotoChannelKe4Short = 'channel_ke4_1_moto',
    /**  摩托科一科四短时提分 */
    MotoChannelKe1Ke4Short = 'channel_ke1ke4live_moto',
    /**  摩托全科 */
    MotoChannelKemuAll = 'channel_kemuall_moto',
    /**  摩托科一模拟考试 */
    ChannelKe1Kcmnmt = 'channel_ke1_kcmn_mt',
    /**  摩托科一模拟考试 */
    ChannelKe4Kcmnmt = 'channel_ke4_kcmn_mt',

    /**  轻型挂车C6科二 */
    GcChannelKe2 = 'channel_ke2_gc',
    /**  轻型挂车C6科四 */
    GcChannelKe4 = 'channel_ke4_gc',
    /** 轻型挂车C6科四单次购买模拟考试 */
    GcChannelKe4Alone = '',
    /**  轻型挂车C6科四短时提分 */
    GcChannelKe4Short = 'channel_ke4_1_gc',

    /*
     | 资格证相关
     */

    /** 客运 */
    KeYun = 'channel_ke1_ky',
    /** 客运模拟考试单次售卖 */
    KeYunAlone = '',
    /** 货运 */
    HuoYun = 'channel_ke1_hy',
    /** 货运模拟考试单次售卖 */
    HuoYunAlone = '',
    /** 危险品 */
    WeiXian = 'channel_ke1_wxp',
    /** 危险品模拟考试单次售卖 */
    WeiXianAlone = '',
    /** 教练员 */
    JiaoLian = 'channel_ke1_jly',
    /** 教练员模拟考试单次售卖 */
    JiaoLianAlone = '',
    /** 出租车 */
    ChuZu = 'channel_ke1_czc',
    /** 出租车模拟考试单次售卖 */
    ChuZuAlone = '',
    /** 网约车 */
    WangYue = 'channel_ke1_wyc',
    /** 网约车模拟考试单次售卖 */
    WangYueAlone = '',
    /** 危险品押运 */
    WeiXianYaYun = 'channel_ke1_wxpyy',
    /** 危险品押运模拟考试单次售卖 */
    WeiXianYaYunAlone = '',
    /** 叉车 */
    ChaChe = 'channel_ke1_chache',
    /** 叉车模拟考试单次售卖 */
    ChaCheAlone = '',
    /** 危险品装卸 */
    WeiXianZhuangXie = 'channel_ke1_wxpzx',
    /** 危险品装卸模拟考试单次售卖 */
    WeiXianZhuangXieAlone = '',
    /** 爆炸品 */
    BaoZha = 'channel_ke1_bzp',
    /** 爆炸品单次售卖 */
    BaoZhaAlone = '',
    /** 爆炸品押运 */
    BaoZhaYaYun = 'channel_ke1_bzpyy',
    /** 爆炸品押运单次售卖 */
    BaoZhaYaYunAlone = '',
    /** 爆炸品装卸 */
    BaoZhaZhuangXie = 'channel_ke1_bzpzx',
    /** 爆炸品装卸单次售卖 */
    BaoZhaZhuangXieAlone = '',
    /** 爆炸品再教育 */
    JiaoLianZaiJiaoyu = 'channel_ke1_jlyzjy',
    /** 爆炸品再教育单次售卖 */
    JiaoLianZaiJiaoyuAlone = '',

    /** 无人机 */
    WuRenJi = 'channel_ke1_wrj',

    /*
     | 组合包
    */

    /** 长辈版科一科四组合包 */
    ElderChannelKe1Ke4Group = 'channel_ke1ke4_group_elder',
    /** 小车科一科四组合包 */
    ChannelKe1Ke4Group = 'channel_ke1ke4_group',
    /** 小车科二科三组合包 */
    ChannelKe2Ke3Group = 'channel_ke2ke3_group_1',
    /** 小车科二科三包含3D组合包 */
    ChannelKe2Ke3GroupNew = 'channel_ke2ke3vip_group',
    /** 小车科三科四组合包 */
    ChannelKe3Ke4Group = 'channel_ke3ke4_group',

    /** 摩托科一科四组合包 */
    MotoChannelKe1Ke4Group = 'channel_ke1ke4_group_moto',
    /** 摩托科二科三组合包 */
    MotoChannelKe2Ke3Group = 'channel_ke2ke3_group_moto',

    /** 轻型挂车C6科二科四组合包 */
    GcChannelKe2Ke4Group = 'channel_ke2ke4_group_gc',
    /** 拍照搜题1 */
    ChannelSearch1 = 'channel_search_1',
    /** 拍照搜题2 */
    ChannelSearch2 = 'channel_search_2',

    /** 科二3d */
    ChannelKe23D = 'channel_ke2_2_for3Dorders',
    /** 科二科三3d */
    ChannelKe2Ke33D = 'channel_ke2ke3_2_for3Dorders',
    /** 科二3d考场包 */
    ChannelKe2Asset3D = 'channel_ke2_asset_for3Dorders',

    ChannelFilterAdvert = 'channel_filterAdvert',

    ChannelFilterAdvert2 = 'channel_filterAdvert_2',

    ChannelFilterAdvert3 = 'channel_filterAdvert_3',

    // 团购
    ChannelKemuAllDutch = 'channel_kemuall_go_dutch',
    // 一人付
    ChannelKemuAllOnePayAll = 'channel_kemuall_one_pay_all'
}


export interface GoodsExtra {
    label: string;
    highlights: Array<{
        highlight: string;
        description: string;
    }>;
}

/** 商品信息 */
export interface GoodsInfo {
    /** 商品名称 */
    name: string;
    /** 商品名称 */
    goodsName: string;
    /** 苹果商品id */
    appleId: string;
    /** 商品ID */
    groupKey: GroupKey;
    channelCode: GroupKey;
    /** 商品权益列表 */
    sessionIds: number[];
    /** 活动类型 */
    activityType: string;

    /** 价格 */
    payPrice: string;
    /** 原价 */
    originalPrice: string;
    /**
     * 建议价
    */
    suggestedPrice: string

    /** 是否购买 */
    bought: boolean;

    /** 有效天数 */
    validDays: string;
    /** 是否过期 */
    expired?: boolean;
    /** 过期时间 */
    expiredTime?: string;

    /** 推荐商品ID */
    recommendGroupKey?: GroupKey;
    /** 加购商品ID */
    recommendPurchaseGroupKeyList?: GroupKey[];

    /** 是否可以升级 */
    upgrade?: boolean;
    /** 升级策略 */
    upgradeStrategyCode?: string;

    /** 是否包含路线 */
    containRoute?: boolean;
    /** 是否包含3D */
    containThreeD?: boolean;
    /** 活动信息 */
    inActivity?: {
        preDiscountPrice: string,
        discountedPrice: string,
        discountStartTime: number,
        discountEndTime: number,
    }
    /** 商品描述 */
    description: string,

    // 新版接口的定义
    /** tip角标和亮点 */
    tips: GoodsExtra
    /** 活动头图相关配置 */
    headConfig: any,
    dataCode: string,
    dataType: string,
    promotionDetailCode: string
    /** 商品使用次数 */
    times: number,
    activityCode: string,
    priceConfigCode: string
}

/** 比价信息 */
export interface ComparePriceInfo {
    allPrice: string,
    savePrice: string,
    groupItems: Array<{
        groupKey: GroupKey,
        name: string,
        price: string,
        description: string
    }>
}

interface GoodsInfoRes {
    goods: {
        hasRoute: boolean
        'goodsName': string,
        'goodsDescription': string,
        'dataCode': string,
        'dataType': number,
        'durationDescription': string,
        'tiku': CarType
    }
    channelCode: string
    uiConfig: {
        'label': string,
        'highlights': { highlight: string, description: string }[]
    }
    promotion: {
        discountStatus: number
        discountStartTime: string
        discountEndTime: string
        uiConfig: {
            'img': string,
            'videoCover': string,
            'video': string,
            'bgc': string,
            'label': string
        }
    }
}

export enum ActivityType {
    // 分销定制
    promotionCustom = 'promotion_custom',
    // 限时促销
    timeLimited = 'time_limited',
    // 新人专享
    newComer = 'new_comer',
    // 升级优惠
    upgrade = 'upgrade',
    // 优秀学员
    excellentStudent = 'excellent_student',
    // 加购
    additionalPurchase = 'additional_purchase',
    // 福袋
    luckyBag = 'lucky_bag'
}

interface PriceRes {
    goods: {
        dataType: number
        dataCode: string
        hasRoute: boolean
        'goodsName': string,
        'goodsDescription': string,
        'durationDescription': string,
        'tiku': CarType
    }
    channelCode: string
    priceConfigCode: string,
    promotionDetailCode: string,
    suggestedPrice: number,
    channelPrice: number,
    salePrice: number,
    appleGoodsId: string,
    activityType: ActivityType,
    activityCode: string,
    userBoughtInfo: {
        effected: boolean,
        'expired': boolean,
        'expireTime': string
    }
}

async function getGoodsDetail(data: { groupKeys: GroupKey[] }) {
    return request({
        url: 'api/web/sales/get-channel-goods-detail.htm',
        hostName: 'squirrel',
        data: {
            channelCodes: data.groupKeys.join(',')
        }
    });
}

export async function getRecommendGoods(data: { groupKeys: GroupKey[] }) {
    return request({
        url: 'api/web/recommend-goods/get-recommend-goods.htm',
        hostName: 'squirrel',
        data: {
            recommendType: 'goods',
            channelCodes: data.groupKeys.join(','),
            patternCode: URLParams.patternCode,
            sceneCode: URLParams.sceneCode
        }
    });
}

export async function getGoodsPrice(data: { groupKeys: GroupKey[], _userCity?: string }) {
    return request({
        url: 'api/web/sales/get-channel-goods-price.htm',
        hostName: 'squirrel',
        data: {
            tiku: URLCommon.tiku,
            channelCodes: data.groupKeys.join(','),
            _userCity: data._userCity
        }
    }).catch(error => {
        if (error.errorCode === 401001) {
            MCProtocol.Core.System.confirm({
                title: '设备超限',
                message: error.message || error.statusText,
                action: '确定',
                cancel: '取消',
                callback: () => {
                    webClose();
                }
            });
        }
    });
}

export async function getGroupSessionInfo(data: { groupKeys: GroupKey[] }): Promise<GoodsInfo[]> {
    const goodInfoList: GoodsInfo[] = [];
    await Promise.all([getGoodsDetail(data), getGoodsPrice(data)])
        .then(async ([goodRes, priceRes]: [{ itemList: GoodsInfoRes[] }, { itemList: PriceRes[] }]) => {

            goodRes.itemList.forEach((item, index) => {
                const priceItem = priceRes.itemList[index];
                priceItem.salePrice *= 100;
                priceItem.channelPrice *= 100;
                const expiredTime = priceItem.userBoughtInfo.expireTime && dateFormat(priceItem.userBoughtInfo.expireTime, 'yyyy.MM.dd HH:mm');
                const payPrice = formatPrice(priceItem.salePrice);

                let inActivity;
                let headConfig;
                if (item.promotion?.discountStatus === 2) {
                    const preDiscountPrice = formatPrice(priceItem.channelPrice);
                    const discountedPrice = String(Math.floor(+preDiscountPrice - +payPrice));

                    inActivity = {
                        preDiscountPrice,
                        discountedPrice,
                        discountStartTime: item.promotion?.discountStartTime,
                        discountEndTime: item.promotion?.discountEndTime
                    };
                    headConfig = {
                        ...item.promotion?.uiConfig
                    };
                }

                const goodInfo: GoodsInfo = {
                    appleId: priceItem.appleGoodsId,
                    bought: priceItem.userBoughtInfo.effected,
                    expired: priceItem.userBoughtInfo.expired,
                    expiredTime,
                    containRoute: item.goods.hasRoute,
                    sessionIds: [data.groupKeys[index]],
                    payPrice,
                    suggestedPrice: priceItem.suggestedPrice,
                    originalPrice: formatPrice(priceItem.channelPrice),
                    upgrade: priceItem.activityType === ActivityType.upgrade,
                    name: item.goods.goodsName,
                    validDays: item.goods.durationDescription,
                    groupKey: data.groupKeys[index],
                    upgradeStrategyCode: priceItem.activityType === ActivityType.upgrade ? priceItem.activityCode : '',
                    description: item.goods.goodsDescription,
                    inActivity,

                    tips: item.uiConfig,
                    headConfig,
                    channelCode: item.channelCode,
                    dataCode: item.goods.dataCode,
                    dataType: item.goods.dataType,
                    promotionDetailCode: priceItem.promotionDetailCode,
                    activityType: priceItem.activityType,
                    activityCode: priceItem.activityCode,
                    priceConfigCode: priceItem.priceConfigCode
                } as unknown as GoodsInfo;

                goodInfoList.push(goodInfo);
            });
        });

    return goodInfoList;
}

export async function getSessionInfo(data: { groupKey: GroupKey }): Promise<GoodsInfo> {
    const goodInfoList = await getGroupSessionInfo({ groupKeys: [data.groupKey] });

    return goodInfoList[0];
}

export async function comparePrice(data: { 
    channelCode: GoodsInfo['channelCode']
    dataType: GoodsInfo['dataType']
    dataCode: GoodsInfo['dataCode']
    activityType: GoodsInfo['activityType']
    activityCode: GoodsInfo['activityCode']
 }): Promise<ComparePriceInfo> {
    const comparePrice = await request({
        url: 'api/web/price-compare/list.htm',
        hostName: 'squirrel',
        data: {
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode,
            ...data
        }
    });

    let allPrice = 0;
    comparePrice?.priceCompareList.forEach((item: any) => {
        item.price = item.price ? item.price * 100 : 0;
        allPrice += item.price;
        item.price = item.price ? formatPrice(item.price) : 0;
    });

    return comparePrice && {
        allPrice: formatPrice(allPrice),
        groupItems: comparePrice.priceCompareList,
        savePrice: formatPrice(+allPrice - (comparePrice.price * 100))
    };
}

export type GroupComparePriceParams = {
    channelCode: GoodsInfo['channelCode']
    dataType: GoodsInfo['dataType']
    dataCode: GoodsInfo['dataCode']
    activityType: GoodsInfo['activityType']
    activityCode: GoodsInfo['activityCode']
}

export async function GroupComparePrice(data: GroupComparePriceParams[]): Promise<Partial<Record<GroupKey, ComparePriceInfo>>> {
    const compareRes = await request({
        url: 'api/web/price-compare/batch-list.htm',
        hostName: 'squirrel',
        method: 'GET',
        data: {
            goodsDataList: JSON.stringify(data),
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode
        }
    });
    const comparePricePool: Partial<Record<GroupKey, ComparePriceInfo>> = {};

    compareRes.itemList.forEach((res: any) => {
        let allPrice = 0;
        res?.priceCompareList.forEach((item: any) => {
            item.price = item.price ? item.price * 100 : '';
            allPrice += item.price;
            item.price = item.price ? formatPrice(item.price) : '';
        });

        comparePricePool[res.channelCode as GroupKey] = {
            ...res,
            allPrice: formatPrice(allPrice),
            upGroupItems: res.priceCompareList || [],
            groupItems: res.priceCompareList || [],
            diffPrice: formatPrice(+allPrice - (res.price * 100)),
            savePrice: formatPrice(+allPrice - (res.price * 100))
        };
    });

    return comparePricePool;
}