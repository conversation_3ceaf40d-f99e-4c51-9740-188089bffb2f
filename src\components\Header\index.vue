<script setup lang="ts">
import { goBack } from '@/common/core';
import systemStore from '@/store/modules/system';
import { computed, useAttrs, ref } from 'vue';
const maxLimit = 200;
const systemInfoStore = systemStore();
const attrs = useAttrs();
const props = defineProps({
    title: String,
    // 'black' | 'white'
    theme: String,
    // 'black' | 'white'
    endTheme: String,
    finalBgColor: String,
    scrollTop: {
        type: Number,
        default: 0,
    },
});

const themeName = computed(() => {
    return (props.scrollTop || 0) > maxLimit / 2 ? props.endTheme : props.theme || 'white';
});

const bgStr = computed(() => {
    const finalBgColor = props.finalBgColor;
    if (finalBgColor) {
        const r = parseInt(finalBgColor.slice(1, 3), 16);
        const g = parseInt(finalBgColor.slice(3, 5), 16);
        const b = parseInt(finalBgColor.slice(5, 7), 16);
        const rgb = [r, g, b].join(',');
        const alpha = props.scrollTop / maxLimit;

        return `rgba(${rgb},${alpha})`;
    }

    const channel = props.theme === 'white' ? 255 : 0;
    const rgb = [channel, channel, channel].join(',');
    const alpha = props.scrollTop / maxLimit;

    return `rgba(${rgb},${alpha})`;
});

const emit = defineEmits<{
    /** 返回按钮点击事件 */
    (e: 'back'): void;
}>();

const onBack = () => {
    if (attrs['onBack']) {
        emit('back');
    } else {
        goBack();
    }
};
const onHelp = async () => {
    // const authToken = await getAuthToken();
    // if (!authToken) {
    //     await login();
    //     return;
    // }
};
</script>

<template>
    <div class="top-header" :style="{ paddingTop: `${systemInfoStore.systemInfo?.statusBarHeight || 0}px`}">
        <div ref="header" class="header" :class="[themeName]" :style="{ paddingTop: `${systemInfoStore.systemInfo?.statusBarHeight || 0}px`, backgroundColor: bgStr }">
            <slot name="back">
                <div class="back" @click="onBack"></div>
            </slot>
            <slot name="title">
                <div class="title">{{ props.title || '驾考宝典' }}</div>
            </slot>
            <slot name="right">
                <div class="help" @click="onHelp"></div>
            </slot>
            <slot name="bottom"></slot>
        </div>
    </div>
</template>

<style lang="less" scoped>
@import './index.less';
</style>
