.protocol-box {
    font-size: 12px;
    display: flex;
    align-items: center;

    &.textMax {
        font-size: 15px;

        span {
            line-height: 21px !important;
        }
    }

    // 图片预加载
    &:after {
        visibility: hidden;
        content: '';
        position: absolute;
        background: url(./images/check_3.png), url(./images/check_4.png);
    }

    .check-box {
        display: inline-block;
        width: 12px;
        height: 12px;
        background: url(./images/check_3.png);
        background-size: 100% 100%;
        margin-right: 4px;

        &.active {
            background: url(./images/check_4.png);
            background-size: 100% 100%;
        }
    }

    .txt {
        color: #333;
        line-height: 17px;

        .noEvent {
            pointer-events: none;
        }

        .go-protocol {
            color: #984321;
        }
    }

    &.elder {
        font-size: 14px;
    }
}

.theme1 {
    .check-box {
        background: url(./images/check_5.png);
        background-size: 100% 100%;

        &.active {
            background: url(./images/check_6.png);
            background-size: 100% 100%;
        }
    }

    .txt {
        color: #666;

        .go-protocol {
            color: #04A5FF;
        }
    }
}

.theme7 {
    .check-box {
        background: url(./images/check_1.png);
        background-size: 100% 100%;

        &.active {
            background: url(./images/check_2.png);
            background-size: 100% 100%;
        }
    }

    .txt {
        color: #AE8A79;

        .go-protocol {
            color: #F3D3C4;
        }
    }
}