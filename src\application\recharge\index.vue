<script setup lang="ts">
import { ref, computed } from 'vue';
import { makeToast } from '@/common/utils';
import { webClose } from '@/common/core';

// 导入图片资源 (占位符，稍后替换)
import backIcon from './images/4644b6.png';
import shareIcon from './images/88147b.png';
import backgroundImg from './images/e0288b.png';
import fcb898Img from './images/fcb898.png';
import diamondIcon from './images/53bb81.png';
import infoIcon from './images/920e2a.png';

// 钻石购买选项数据
const diamondOptions = ref([
  {
    id: 'diamond300',
    amount: 300,
    price: 30,
    isSelected: true
  },
  {
    id: 'diamond500',
    amount: 500,
    price: 50,
    isSelected: false
  },
  {
    id: 'diamond1000',
    amount: 1000,
    price: 100,
    isSelected: false
  },
  {
    id: 'diamond2000',
    amount: 2000,
    price: 200,
    isSelected: false
  },
  {
    id: 'diamond5000',
    amount: 5000,
    price: 500,
    isSelected: false
  },
  {
    id: 'diamond10000',
    amount: 10000,
    price: 1000,
    isSelected: false
  }
]);

// 支付方式
const paymentMethods = ref([
  { id: 'alipay', name: '支付宝支付', isSelected: false },
  { id: 'wechat', name: '微信支付', isSelected: true }
]);

// 用户当前钻石数量
const currentDiamonds = ref(180.00);

// 是否同意协议
const agreeToTerms = ref(false);

// 选择钻石选项
const selectDiamondOption = (optionId: string) => {
  diamondOptions.value.forEach(option => {
    option.isSelected = option.id === optionId;
  });
};

// 选择支付方式
const selectPaymentMethod = (methodId: string) => {
  paymentMethods.value.forEach(method => {
    method.isSelected = method.id === methodId;
  });
};

// 获取选中的钻石选项
const selectedOption = computed(() => {
  return diamondOptions.value.find(option => option.isSelected);
});

// 获取选中的支付方式
const selectedPayment = computed(() => {
  return paymentMethods.value.find(method => method.isSelected);
});

// 处理购买
const handlePurchase = async () => {
  if (!selectedOption.value) {
    makeToast('请选择购买额度');
    return;
  }

  if (!selectedPayment.value) {
    makeToast('请选择支付方式');
    return;
  }

  if (!agreeToTerms.value) {
    makeToast('请先阅读并同意会员协议');
    return;
  }

  // 这里应该调用实际的购买接口
  makeToast('正在跳转支付...');
};

// 切换协议同意状态
const toggleAgreement = () => {
  agreeToTerms.value = !agreeToTerms.value;
};

// 返回按钮
const handleBack = () => {
  webClose();
};

// 分享按钮
const handleShare = () => {
  makeToast('分享功能');
};
</script>

<template>
  <div class="recharge-page">

    <!-- 导航栏 -->
    <div class="navigation">
      <img :src="backIcon" alt="返回" class="nav-icon" @click="handleBack" />
      <div class="nav-title">我的创作</div>
      <img :src="shareIcon" alt="分享" class="nav-icon" @click="handleShare" />
    </div>

    <div class="main">
      <!-- 顶部区域 -->
      <div class="header-section">
        <!-- 顶部背景图 -->
        <div class="header-background" :style="{ backgroundImage: `url(${backgroundImg})` }"></div>

        <!-- 钻石余额卡片 -->
        <div class="diamond-card" :style="{ backgroundImage: `url(${fcb898Img})` }">
          <div class="diamond-info">
            <div class="diamond-label">我的钻石</div>
            <div class="diamond-amount">
              <span class="amount-integer">{{ Math.floor(currentDiamonds) }}</span>
              <span class="amount-dot">.</span>
              <span class="amount-decimal">{{ String(currentDiamonds % 1).slice(2).padEnd(2, '0') }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 选择购买额度 -->
      <div class="purchase-section">
        <h2 class="section-title">请选择钻石购买额度</h2>

        <!-- 钻石选项网格 -->
        <div class="diamond-grid">
          <div v-for="option in diamondOptions" :key="option.id" class="diamond-option" :class="{
            'selected': option.isSelected
          }" @click="selectDiamondOption(option.id)">
            <div class="option-content">
              <img :src="diamondIcon" alt="钻石" class="option-icon" />
              <div class="option-amount">{{ option.amount }}</div>
            </div>
            <div class="option-price">{{ option.price }}元</div>
          </div>
        </div>

        <!-- 有效期说明 -->
        <div class="validity-info">
          <img :src="infoIcon" alt="信息" class="info-icon" />
          <span class="validity-text">灵感值有效期：2年</span>
          <span class="rules-link">规则详情</span>
        </div>
      </div>

    </div>

    <!-- 底部支付区域 -->
    <div class="payment-section">
      <!-- 支付方式选择 -->
      <div class="payment-methods">
        <div v-for="method in paymentMethods" :key="method.id" class="payment-method"
          :class="{ 'selected': method.isSelected }" @click="selectPaymentMethod(method.id)">
          <div class="payment-icon" :class="method.id"></div>
          <span class="payment-name">{{ method.name }}</span>
        </div>
      </div>

      <!-- 购买按钮 -->
      <div class="purchase-button" @click="handlePurchase">
        <span class="purchase-price">¥{{ selectedOption?.price || 30 }}</span>
        <span class="purchase-text">立即购买</span>
      </div>

      <!-- 协议同意 -->
      <div class="agreement-section">
        <div class="checkbox" :class="{ 'checked': agreeToTerms }" @click="toggleAgreement"></div>
        <span class="agreement-text">
          我已阅读并同意图灵灵的<span class="agreement-link">会员协议</span>
        </span>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.recharge-page {
  height: 100vh;
  background: hsla(0, 0%, 4%, 1);
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.navigation {
  position: absolute;
  z-index: 1;
  left: 15px;
  top: 54px;
  width: 345px;
  height: 25px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .nav-icon {
    width: 25px;
    height: 25px;
    cursor: pointer;
  }

  .nav-title {
    color: hsla(0, 0%, 100%, 1);
    font-size: 15px;
    line-height: 14px;
    font-weight: bold;
  }
}

.main {
  flex: 1;
  height: 0;
  overflow-y: auto;
}

.header-section {
  position: relative;
  height: 230px;
  flex-shrink: 0;

  .header-background {
    position: absolute;
    left: 0;
    top: 0;
    width: 375px;
    height: 200px;
    background-size: cover;
    background-position: center;
  }
}

.diamond-card {
  position: absolute;
  left: 20px;
  top: 108px;
  width: 335px;
  height: 122px;
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  padding-left: 24px;
  box-sizing: border-box;

  .diamond-info {
    color: white;
  }

  .diamond-label {
    font-size: 14px;
    line-height: 20px;
    margin-bottom: 8px;
  }

  .diamond-amount {
    display: flex;
    align-items: baseline;
  }

  .amount-integer,
  .amount-dot,
  .amount-decimal {
    font-size: 38px;
    line-height: 45px;
    font-weight: bold;
  }
}

.purchase-section {
  padding: 20px 15px 0;
}

.section-title {
  color: hsla(0, 0%, 100%, 1);
  font-size: 16px;
  line-height: 22px;
  font-weight: bold;
  margin: 0 0 17px 0;
}

.diamond-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12px 9px;
  margin-bottom: 20px;
}

.diamond-option {
  width: 109px;
  height: 92px;
  background: hsla(230, 11%, 11%, 1);
  border-radius: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
  padding: 19px 0 0 0;
  box-sizing: border-box;
  overflow: hidden;

  &.selected {
    background: linear-gradient(135deg, hsla(196, 100%, 27%, 1) 0%, hsla(217, 65%, 22%, 1) 41%, hsla(231, 19%, 14%, 1) 100%);
    border: 2px solid hsla(184, 100%, 54%, 1);
  }

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 27px;
    background: hsla(0, 0%, 100%, 0.04);
    border-radius: 0 0 10px 10px;
  }

  &.selected::after {
    background: linear-gradient(135deg, hsla(208, 100%, 72%, 1) 0%, hsla(212, 76%, 59%, 1) 17%, hsla(290, 100%, 73%, 1) 86%, hsla(295, 100%, 72%, 1) 100%);
    border-radius: 0 0 8px 8px;
    height: 25px;
    left: 0;
    right: 0;
  }

  .option-content {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .option-amount {
    color: hsla(0, 0%, 100%, 1);
    font-size: 28px;
    line-height: 32px;
    font-weight: bold;
    margin-bottom: -3px;
  }

  .option-icon {
    width: 20px;
    height: 20px;
    margin-right: 2px;
  }

  .option-price {
    position: absolute;
    bottom: 4px;
    left: 50%;
    transform: translateX(-50%);
    color: hsla(0, 0%, 100%, 1);
    font-size: 14px;
    line-height: 20px;
    z-index: 1;
  }
}

.validity-info {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 20px;

  .info-icon {
    width: 15px;
    height: 15px;
    margin-bottom: 2px;
  }

  .validity-text {
    color: hsla(0, 0%, 40%, 1);
    font-size: 13px;
    line-height: 18px;
    flex: 1;
  }

  .rules-link {
    color: hsla(0, 0%, 100%, 1);
    font-size: 13px;
    line-height: 18px;
    cursor: pointer;
  }
}

.payment-section {
  height: 168px;
  background: hsla(225, 11%, 7%, 1);
  border-radius: 10px 10px 0 0;
  padding: 14px 16px;
  box-sizing: border-box;

  .payment-methods {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 14px;

    .payment-method {
      width: 144px;
      height: 32px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
      cursor: pointer;

      &.selected {
        background: hsla(0, 0%, 100%, 0.13);
      }
    }

    .payment-icon {
      width: 20px;
      height: 20px;
      border-radius: 100%;

      &.alipay {
        background: hsla(215, 100%, 62%, 1);
      }

      &.wechat {
        background: hsla(120, 100%, 40%, 1);
      }
    }

    .payment-name {
      color: hsla(0, 0%, 100%, 1);
      font-size: 14px;
      line-height: 20px;
    }
  }

  .purchase-button {
    width: 343px;
    height: 50px;
    background: linear-gradient(305deg, rgba(223, 126, 236, 0.00) 27%, #df7eec 88%), linear-gradient(135deg, #2c89fe 1%, #2e6dff);
    border-radius: 12px;
    box-shadow: 0px 4px 12px -4px rgba(21, 22, 22, 0.50);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    cursor: pointer;
    margin-bottom: 10px;
  }

  .purchase-price,
  .purchase-text {
    color: hsla(0, 0%, 100%, 1);
    font-size: 18px;
    line-height: 25px;
  }

  .agreement-section {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 7px;
    padding: 0 22px;
    margin-bottom: 10px;
  }

  .checkbox {
    width: 15px;
    height: 15px;
    border: 2px solid hsla(60, 3%, 66%, 1);
    border-radius: 50%;
    cursor: pointer;
    margin-bottom: 2px;

    &.checked {
      background: hsla(213, 99%, 58%, 1);
      border-color: hsla(213, 99%, 58%, 1);
    }
  }

  .agreement-text {
    color: hsla(60, 4%, 53%, 1);
    font-size: 13px;
    line-height: 18px;
  }

  .agreement-link {
    color: hsla(0, 0%, 100%, 1);
    cursor: pointer;
  }
}
</style>
