<script setup lang="ts">
import { computed } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();
// 检查当前路由是否是首页
const needKeepAlive = computed(() => route.name === 'home');
</script>

<template>
    <div class="app-box">
        <RouterView v-slot="{ Component }">
            <keep-alive>
                <component :is="Component" v-if="needKeepAlive" />
            </keep-alive>
            <component :is="Component" v-if="!needKeepAlive" />
        </RouterView>
    </div>
</template>

<style lang="less" scoped>
.app-box {
    height: 100vh;
}
</style>
