import { computed, ref } from "vue";
import { defineStore } from "pinia";
import { Platform } from "@/common/env";
import { getUserInfo as getUserInfoFn } from '@/common/core';


const userInfo = defineStore("wanmei_userInfo", () => {
  const userInfo = ref();

  const getUserInfo = computed(() => userInfo.value)

  function setUserInfo(data: any) {
    userInfo.value = data;
  }

  if(Platform.isInApp){
    getUserInfoFn().then(info => {
      console.log(info);
      setUserInfo(info)
    })
  }

  return { userInfo, getUserInfo, setUserInfo };
}, {
  persist: {
    serializer: {
      serialize: (state) => JSON.stringify(state.userInfo),
      deserialize: (data) => {
        return { userInfo: JSON.parse(data) }
      },
    },
  }
});

export default userInfo;