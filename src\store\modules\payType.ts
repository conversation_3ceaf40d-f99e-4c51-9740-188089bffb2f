import { computed, ref } from "vue";
import { defineStore } from "pinia";
import { PayType, Platform } from "@/common/env";
import { promisify } from "@/common/utils";
import { MCProtocol } from '@simplex/simple-base';


const payTypeStore = defineStore("payType_info", () => {
    const payTypeList = ref<{
        payType: PayType,
        text: string
    }[]>([]);
    const payType = ref<PayType>(PayType.Weixin);

    function setPayType(type: PayType) {
        payType.value = type
    }

    const list: any[] = [];

    new Promise<void>((resolve) => {
        if (Platform.isInApp) {
            if (Platform.isIOS) {
                list.push({
                    payType: PayType.ApplePay,
                    text: '苹果支付'
                });
                resolve();
            } else {
                promisify(MCProtocol.Pay.channels)().then((data) => {
                    const isWeixinAvailable = data.data.wx;
                    const isHarmonyAvailable = !!Platform.isHarmony && data.data.huaweiIap;

                    if (isHarmonyAvailable) {
                        list.push({
                            payType: PayType.Harmony,
                            text: '华为支付'
                        });
                    }

                    if (isWeixinAvailable) {
                        list.push({
                            payType: PayType.Weixin,
                            text: '微信支付'
                        });
                    }

                    list.push({
                        payType: PayType.Alipay,
                        text: '支付宝支付'
                    });

                    resolve();

                })
            }
        } else {
            list.push({
                payType: PayType.Weixin,
                text: '微信支付'
            });
            list.push({
                payType: PayType.Alipay,
                text: '支付宝支付'
            });
            resolve();
        }
    }).then(() => {
        payType.value = list[0].payType;

        payTypeList.value = list;
    })

    return { payTypeList, payType, setPayType };
});

export default payTypeStore;