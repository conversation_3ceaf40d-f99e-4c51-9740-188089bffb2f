import { ConcreteSubscriber, Publisher, SubEvent } from "./index";

export enum LoginType {
    success = 'loginSuccess',
    fail = 'loginFail',
    cancel = 'loginCancel'
}

const loginPublisher = new Publisher();
export function ListenLogin(fn: (event: SubEvent) => void, isOnce: boolean = false) {
    const loginSubscriber = new ConcreteSubscriber(fn, isOnce);

    loginPublisher.subscribe(loginSubscriber);
}

export function notifyLogin(type: string, authtoken: string) {
    loginPublisher.publish({ type, data: authtoken })
}


