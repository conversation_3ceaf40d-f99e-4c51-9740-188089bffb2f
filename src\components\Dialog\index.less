.modal {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;

    &.hide {
        display: none;
    }

    &.invisible {
        visibility: hidden;
    }

    .modal-content {
        position: relative;
        border-radius: 8px;
        background-color: #fff;
        display: flex;
        flex-direction: column;
        justify-content: center;
        width: 315px;
        box-sizing: border-box;
        padding: 15px;

        .close {
            position: absolute;
            top: 5px;
            right: 5px;
            width: 40px;
            height: 40px;
            background: url(./images/close2.png) no-repeat center center/cover;
            background-size: 25px 25px;
        }

        .close-bottom {
            position: absolute;
            bottom: -48px;
            left: 50%;
            transform: translateX(-50%);
            width: 28px;
            height: 28px;
            background: url(./images/close.png);
            background-size: cover;
        }

        &.center {
            border-radius: 10px;
        }

        .content-body {
            text-align: center;
            font-size: 14px;
            margin-top: 10px;
            line-height: 20px;
            color: #666;
        }

        .title {
            text-align: center;
            color: #333;
            font-size: 18px;
            font-weight: bold;
        }

        .footer {
            display: flex;
            justify-content: center;

            .footer-active {
                width: 135px;
                height: 44px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 16px;
                border-radius: 30px;
                margin-top: 15px;

                &.cancle {
                    border: 1px solid #333;
                    margin-right: 15px;
                }

                &.ok {
                    flex: 1;
                    background: linear-gradient(90deg, rgba(0, 224, 229, 1) 0%, rgba(0, 134, 250, 1) 100%);
                    color: white;
                }
            }
        }
    }
}
