.top-header {
    height: 48px;
    box-sizing: content-box;
    position: relative;
    z-index: 100;

    .header {
        display: flex;
        align-items: center;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        padding: 0 15px;
        max-width: 375px;
        margin: 0 auto;
        height: 48px;
        box-sizing: content-box;

        .back {
            width: 25px;
            height: 25px;
            background-size: 25px 25px;
            background-repeat: no-repeat;
            background-position: center;
            box-sizing: content-box;
        }

        .title {
            flex: 1;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .help {
            width: 22px;
            height: 22px;
            background-size: 22px 22px;
            background-repeat: no-repeat;
            background-position: center;
            box-sizing: content-box;
        }

        &.white {
            background-color: white;

            .title {
                color: #333;
            }

            .back {
                background-image: url(./images/black_back.png);
            }

            .help {
                background-image: url(./images/black_kf.png);
            }
        }

        &.black {
            background-color: black;

            .title {
                color: white;
            }

            .back {
                background-image: url(./images/back.png);
            }

            .help {
                background-image: url(./images/kf.png);
            }
        }
    }
}

.header {
    display: flex;
    align-items: center;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    padding: 0 15px;
    max-width: 375px;
    margin: 0 auto;
    height: 48px;
    box-sizing: content-box;
}

.header .back {
    width: 25px;
    height: 25px;
    background-size: 25px 25px;
    background-repeat: no-repeat;
    background-position: center;
    box-sizing: content-box;
}

.header .title {
    flex: 1;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.header .help {
    width: 22px;
    height: 22px;
    background-size: 22px 22px;
    background-repeat: no-repeat;
    background-position: center;
    box-sizing: content-box;
}

.header.white {
    background-color: white;
}

.header.white .title {
    color: #666;
}

.header.white .back {
    background-image: url(./images/black_back.png);
}

.header.white .help {
    background-image: url(./images/black_kf.png);
}

.header.black {
    background-color: black;
}

.header.black .title {
    color: white;
}

.header.black .back {
    background-image: url(./images/back.png);
}

.header.black .help {
    background-image: url(./images/kf.png);
}