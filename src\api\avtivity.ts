import { CarType, KemuType, PayType, URLParams } from "@/common/env";
import { PayRef } from "@/common/pay";
import { request } from "@/common/request";
import payTypeStore from "@/store/modules/payType";
import { CreateOrderParams, PlatformType } from "./pay";

export async function getActivitInfoById(data: { activityId: string | number }): Promise<any> {
    const res = await request({
        hostName: 'activity',
        url: 'api/web/activity/base-info-by-id.htm',
        data: data
    });

    return res;
}

export async function createGiftVipOrder(data: CreateOrderParams & {
    cardCode: string;
    words: string;
    friendPhone: string;
}): Promise<{
    orderNumber: string;
    content: string;
    paid: boolean;
    giftCode: string;
}> {
    const payTypeInfo = payTypeStore();
    return request({
        hostName: 'activity',
        url: 'api/open/gift-vip/place-order.htm',
        method: 'POST',
        data: {
            orderRef: PayRef,
            payType: payTypeInfo.payType,
            platformType: payTypeInfo.payType === PayType.Harmony ? PlatformType.Harmony : PlatformType.MOBILE,
            pageData: JSON.stringify({
                pageName: '未知',
                kemu: URLParams.kemuStyle || KemuType.Ke1,
                carStyle: URLParams.carStyle || CarType.CAR,
                sceneCode: URLParams.sceneCode,
                patternCode: URLParams.patternCode,
                fromPathCode: '000001',
                fromPageCode: '100000',
            }),
            ...data
        }
    });
}

export interface GiftVipDetail {
    "launchUserPhoneMask": string,
    "cardCode": string,
    "words": string,
    "channelName": string
}

export function queryGiftVipDetail(data: {
    giftCode: string
}): Promise<GiftVipDetail> {
    return request({
        hostName: 'activity',
        url: 'api/web/gift-vip/query-gift-vip-detail.htm',
        data
    });
}