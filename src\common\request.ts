/*
 * ------------------------------------------------------------------
 * 网络请求
 * ------------------------------------------------------------------
 */

import { MCBaseStore } from '@simplex/simple-base';
import { makeToast, sign } from './utils';
import { Platform, URLParams } from './env';
import Jump from '@/common/navigate';
import { getAuthToken } from './core';

type HostName = 'activity' | 'squirrel' | 'login' | 'apiShare';

interface RequestOptions {
    hostName?: HostName,
    url: string,
    method?: 'GET' | 'POST',
    headers?: Record<string, any>,
    data?: Record<string, any>
    others?: any
    noToast?: boolean
    returnStore?: boolean
}

const signMap: Record<HostName, string> = {
    'activity': '*#06#j5moQpWNkIhrjaSFgodFh52T',
    'squirrel': '*#06#iKVuc32KRW12cqg8QnGkdX16',
    'login': '',
    'apiShare': '',
};

const resignMap: Record<HostName, string> = {
    'activity': 'hello',
    'squirrel': 'helloworld',
    'login': '',
    'apiShare': '',
};

var authTokenFn = async function () {
    if (Platform.isInApp) {
        return getAuthToken();
    }
    const authToken = JSON.parse(localStorage.getItem('wanmei_userInfo') || '{}')?.authToken || '';
    return authToken
}
let isJumpLogin = false;
/** 发起网络请求 */
export async function request({ hostName = 'activity', url, method = 'GET', headers, data = {}, others = {}, noToast, returnStore }: RequestOptions): Promise<any> {
    /*
     | 处理参数
     */
    let params: any = {};
    const authToken = await authTokenFn();

    if (authToken && !data.authToken) {
        data.authToken = authToken;
    }

    if (!Platform.isInApp) {
        data = {
            ...(() => {
                const whiteUrlParams: Record<string, string> = {};
                for (const key in URLParams) {
                    if (!(/^_/).test(key)) {
                        whiteUrlParams[key] = URLParams[key];
                    }
                }
                return whiteUrlParams;
            })(),
            ...data,
            _r: sign(1),
            resign: resignMap[hostName]
        };
    }

    // 如果要用json形式发请求，参数需要JSON.stringify
    if (others?.ajaxOptions) {

        params = JSON.stringify(data);
    } else {
        params = data;
    }
    return new Promise((resovle, reject) => {
        (new (MCBaseStore.extend({
            clientVersion: 2048,
            url: `${hostName}://${url}`,
            sign: signMap[hostName],
            headers,
            method: method.toLocaleUpperCase(),
            ...others,
            errorToast: false,
            type: 'online'
        }))()).request(params).then((res: any, store: any) => {
            // console.info('[response]', url, res);
            resovle(returnStore ? store.data : res);
        }).fail((_errorCode: number, error: any) => {
            const text = JSON.parse(error.text);
            if (!noToast) {
                makeToast(error.statusText || '');
            }
            if (text.errorCode === 403 && !isJumpLogin) {
                isJumpLogin = true;
                if (localStorage.getItem('wanmei_userInfo')) {
                    localStorage.removeItem('wanmei_userInfo');
                    localStorage.removeItem('vip-active-authToken');
                    Jump.reload();
                }

            }
            reject(error);
        });
    });
}
