import { CarType, KemuType, URLCommon, URLParams } from './env';

export type EnumKeys<T> = T extends { [key: string]: infer V } ? keyof T : never;

export enum ProtocolUrl {
    YINSI = 'https://laofuzi.kakamobi.com/agreements/privateAgreement.html?_productCategory=jiakaobaodian&_product=驾考宝典',
    USER_SERVER = 'https://laofuzi.kakamobi.com/agreements/userAgreement.html?_productCategory=jiakaobaodian&_product=驾考宝典',
    PROTOCOL1_URL = 'https://laofuzi.kakamobi.com/protocol/protocol.html?protocolKey=jkbdVIP'
}
export const defaultImg = 'https://web-resource.mc-cdn.cn/minprogram/dybxb/<EMAIL>'

/** 科目文案 */
const kemuTxt = {
    [KemuType.Ke1]: '科目一',
    [KemuType.Ke2]: '科目二',
    [KemuType.Ke3]: '科目三',
    [KemuType.Ke4]: '科目四'
};

/** 车型文案 */
const carStyleTxt = {
    [CarType.CAR]: '小车',
    [CarType.BUS]: '客车',
    [CarType.TRUCK]: '货车',
    [CarType.MOTO]: '摩托车',
    [CarType.GUACHE]: '轻型牵引挂车'
};

/** 车执照类型文案 */
const carStyleLicenseTxt = {
    [CarType.CAR]: 'C1/C2/C3',
    [CarType.BUS]: 'A1/A3/B1',
    [CarType.TRUCK]: 'A2/B2',
    [CarType.MOTO]: 'D/E/F',
    [CarType.GUACHE]: 'C6'
};

/**
 * 产品名称
*/
const productName = URLParams._product === '驾考宝典助手' ? '驾考宝典' : (URLParams._product || '驾考宝典');


export default {
    kemuTxt,
    productName
};