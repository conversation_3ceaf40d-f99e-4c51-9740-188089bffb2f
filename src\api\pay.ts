import { PayType } from "@/common/env";
import { GoodsInfo } from "./goods";

export enum PlatformType {
    WAP = 'wap',
    MOBILE = 'mobile',
    WEB = 'web',
    WEIXINH5 = 'weixin_h5',
    WEIXINAPPLET = 'weixin_applet',
    QRCODE = 'qr_code',
    DOUYIN = 'douyin',
    Harmony = 'huawei_iap'
}

/** 创建订单参数 */
export interface CreateOrderParams {

    /** 商品名称 */
    goodsName: GoodsInfo['goodsName']
    /** 商品ID */
    channelCode: GoodsInfo['channelCode']
    /** 苹果ID */
    appleGoodsId?: GoodsInfo['appleId']
    /** 支付方式 */
    payType?: PayType;
    /** 优惠券code */
    couponCode?: string;
    /** 活动类型 */
    activityType: GoodsInfo['activityType']
    /** 活动类型 */
    activityCode: GoodsInfo['activityCode'];
    /** 额外订单信息 */
    extraInfo?: string;
    goodsDataCode: GoodsInfo['dataCode']
    goodsDataType: GoodsInfo['dataType']
    platformType?: PlatformType,
    openId?: string
}

export enum PayStatusEnum {
    PAY_SUCCESS = 1,
    PAY_FAIL = 2,
    PAY_CANCEL = 3
}
