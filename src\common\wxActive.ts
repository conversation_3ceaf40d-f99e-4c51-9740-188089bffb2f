import { URLParams } from './env';
import { request } from './request';

export const weixinAuth = async (appId: string) => {

    const code = URLParams.code;
    const tokenData = sessionStorage.getItem('tokenData');

    if (tokenData) {
        return JSON.parse(tokenData);
    }
    if (!code || code === 'undefined') {
        const params = URLParams;
        let searchStr = '';
        for (const k in params) {
            if (Object.prototype.toString.call(params[k]) === '[object Array]') {
                params[k] = params[k][0];
            }

            if (k === 'shareData') {
                let shareData: any = params[k];
                try {
                    shareData = JSON.parse(shareData);
                    shareData.url = encodeURIComponent(shareData.url);
                    shareData.iconUrl = encodeURIComponent(shareData.iconUrl);
                    params[k] = JSON.stringify(shareData);
                } catch (e) {
                    params[k] = '';
                }
            }
            searchStr += (k + '=' + (params[k]) + '&');
        }
        searchStr = searchStr.substr(0, searchStr.length - 1);
        const url = encodeURIComponent(window.location.protocol + '//' + window.location.host + window.location.pathname + '?') + (encodeURIComponent(searchStr));
        const hashstr = decodeURIComponent(window.location.hash);
        const authObj = {
            appid: appId,
            redirect_uri: url.replace(hashstr, ''),
            response_type: 'code',
            scope: 'snsapi_base',
            state: hashstr.replace('#', '')
        };

        location.href = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' + authObj.appid + '&redirect_uri=' + (authObj.redirect_uri) + '&response_type=' + authObj.response_type + '&scope=' + authObj.scope + '&state=' + encodeURIComponent(authObj.state) + '#wechat_redirect';
        return '';
    }

    window.location.hash = URLParams.state;

    return request({
        hostName: 'apiShare',
        url: 'api/open/sign/token.htm',
        data: {
            code: code,
            weixinApp: appId
        }
    }).then(res => {
        sessionStorage.setItem('tokenData', JSON.stringify(res));
        return res;
    }).catch((error) => {
        console.log(error, 1111111111);
    });
};

export const wxConfig = (config: { appId: string }) => {
    return new Promise<void>((resolve) => {
        request({
            hostName: 'apiShare',
            data: {
                weixinApp: config.appId,
                url: window.location.href.replace(/#.*/ig, '')
            },
            url: 'api/open/sign/jssign.htm'
        }).then(data => {
            window.wx.config({
                debug: URLParams.debug || false,
                appId: config.appId,
                timestamp: data.timestamp,
                nonceStr: data.noncestr,
                signature: data.signature,
                jsApiList: [
                    'checkJsApi',
                    'updateAppMessageShareData',
                    'updateTimelineShareData',
                    'onMenuShareTimeline',
                    'onMenuShareAppMessage',
                    'onMenuShareQQ',
                    'onMenuShareWeibo',
                    'hideMenuItems',
                    'showMenuItems',
                    'hideAllNonBaseMenuItem',
                    'showAllNonBaseMenuItem',
                    'translateVoice',
                    'startRecord',
                    'stopRecord',
                    'onRecordEnd',
                    'playVoice',
                    'pauseVoice',
                    'stopVoice',
                    'uploadVoice',
                    'downloadVoice',
                    'chooseImage',
                    'previewImage',
                    'uploadImage',
                    'downloadImage',
                    'getNetworkType',
                    'openLocation',
                    'getLocation',
                    'hideOptionMenu',
                    'showOptionMenu',
                    'closeWindow',
                    'scanQRCode',
                    'chooseWXPay',
                    'openProductSpecificView',
                    'addCard',
                    'chooseCard',
                    'openCard'
                ],
                openTagList: [
                    'wx-open-launch-app',
                    'wx-open-launch-weapp',
                    'wx-open-subscribe',
                    'wx-open-audio'
                ]
            });
            window.wx.ready(() => {
                resolve();
            });
        });
    });
};