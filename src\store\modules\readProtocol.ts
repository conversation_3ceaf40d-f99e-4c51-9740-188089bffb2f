import { ref } from "vue";
import { defineStore } from "pinia";

export async function hasCheckbox() {
    return true;
}

const storageKey = location.protocol + '//' + location.host + location.pathname;

export async function hasReaded() {
    // 1、默认配置为需要手动勾选；未取到远程配置展示勾选框，需要勾选后，才能点击购买；
    //    如未勾选协议，点击购买，toast提示：请您先同意《驾考宝典会员协议》，同时该句文案有个震动提醒的效果，同登录页面的提醒；
    // 2、配置布尔值：false-无勾选框，默认为已勾选状态，点击购买，直接进行购买；true-有勾选框，同默认配置说明；
    const storageReaded = window.localStorage.getItem(storageKey);
    if (storageReaded === 'true') {
        return true;
    }
    return !await hasCheckbox();
}

const readProtocolStore = defineStore("readProtocol_info", () => {
    const hasReadInfo = ref(false);
    const hasCheckboxInfo = ref(false);

    function setHasRead(flag: boolean){
        hasReadInfo.value = flag
    }

    hasReaded().then(flag => {
        hasReadInfo.value = flag
    })

    function setHasCheckbox(flag: boolean) {
        hasCheckboxInfo.value = flag
    }

    hasCheckbox().then((flag) => {
        hasCheckboxInfo.value = flag;
    })

    return { hasCheckboxInfo, hasReadInfo, setHasRead, setHasCheckbox };
});

export default readProtocolStore;