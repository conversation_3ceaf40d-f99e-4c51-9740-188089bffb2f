// 定义一个Event接口，用于描述事件
export interface SubEvent {
    type: string;
    data: any;
}

// 定义一个Subscriber接口，用于描述订阅者
interface Subscriber {
    notify(event: SubEvent): void;
    once: boolean; // 添加一个标志，表示是否只订阅一次
}

// 定义一个Publisher类，用于发布事件
export class Publisher {
    private subscribers: Subscriber[] = [];

    // 添加订阅者
    subscribe(subscriber: Subscriber) {
        this.subscribers.push(subscriber);
    }

    // 添加一个只订阅一次的订阅者
    subscribeOnce(subscriber: Subscriber) {
        subscriber.once = true; // 设置只订阅一次的标志
        this.subscribers.push(subscriber);
    }

    // 移除订阅者
    unsubscribe(subscriber: Subscriber) {
        this.subscribers = this.subscribers.filter(s => s !== subscriber);
    }

    // 发布事件
    publish(event: SubEvent) {
        // 创建一个副本，以防在迭代过程中修改数组
        const subscribersCopy = [...this.subscribers];
        subscribersCopy.forEach(subscriber => {
            subscriber.notify(event);
            if (subscriber.once) {
                this.unsubscribe(subscriber); // 如果是只订阅一次的，发布后移除
            }
        });
    }
}

// 定义一个具体的Subscriber类
export class ConcreteSubscriber implements Subscriber {
    once: boolean = false; // 默认不是只订阅一次
    fn: (event: SubEvent) => void
    constructor(fn: (event: SubEvent) => void, isOnce: boolean){
        this.once = isOnce
        this.fn = fn
    }
    notify(event: SubEvent) {
        this.fn(event);
    }
}

// 使用示例
// export const publisher = new Publisher();
// export const subscriber = new ConcreteSubscriber();
// export const onceSubscriber = new ConcreteSubscriber();


