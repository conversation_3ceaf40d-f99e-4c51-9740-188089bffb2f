<script setup lang="ts">
import readProtocolStore from '@/store/modules/readProtocol';
import Texts, { ProtocolUrl } from '@/common/const';
import Jump from '@/common/navigate';

const readProtocolInfo = readProtocolStore();

const props = defineProps(['theme', 'textMax', 'protocolText1', 'hideCheckbox', 'protocolList', 'protocolText2', 'protocolUrl']);

const onChangeReaded = () => {
    readProtocolInfo.setHasRead(!readProtocolInfo.hasReadInfo);
};
const onGoProtocol = (url: string) => {
    Jump.openWeb({
        url: url,
    });
};
</script>
<template>
    <div class="protocol-box" :class="[`theme${props.theme}`, props.textMax ? 'textMax' : '']">
        <span
            class="hotArea check-box"
            :class="{
                active: readProtocolInfo.hasReadInfo,
                hide: !readProtocolInfo.hasCheckboxInfo || props.hideCheckbox,
            }"
            @click="onChangeReaded"
        ></span>
        <span class="txt">
            <template v-if="props.hideCheckbox">
                <span :class="{ noEvent: !readProtocolInfo.hasCheckboxInfo }">{{ props.protocolText1 || '已阅读并同意' }}</span>
            </template>
            <template v-else>
                <span :class="{ noEvent: !readProtocolInfo.hasCheckboxInfo }" @click="onChangeReaded">{{ props.protocolText1 || '已阅读并同意' }}</span>
            </template>

            <template v-if="props.protocolList?.length">
                <span v-for="(item, index) in props.protocolList" :key="item.url" class="go-protocol" @click="onGoProtocol(item.url)">
                    《{{ item.text }}》
                    <template v-if="index < props.protocolList.length - 2"> 、 </template>
                    <template v-else-if="index === props.protocolList.length - 2">
                        <span style="color: #333">和</span>
                    </template>
                </span>
            </template>

            <template v-else>
                <span class="go-protocol" @click="onGoProtocol(ProtocolUrl.PROTOCOL1_URL)">
                    {{ props.protocolText2 || '《' + Texts.productName + '会员协议》' }}
                </span>
            </template>
        </span>
    </div>
</template>

<style lang="less" scoped>
@import './index.less';
</style>
