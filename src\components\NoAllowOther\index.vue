<script setup lang="ts">
import { Platform } from '@/common/env';
import { ref } from 'vue';

const allowShow = ref(Platform.isWeiXin || Platform.isAliPay);
</script>

<template>
    <template v-if="allowShow"> 
        <slot></slot>
    </template>
    <template v-else>
        <div class="page-home-noallow page-container">
            <div class="info-box">
                <div class="img1"></div>
                <div class="dec1">请使用微信或支付宝扫码</div>
                <div class="dec2">打开当前页面</div>
            </div>
            <div class="logo-dec">
                <div class="logo-icon"></div>
                <div class="dec-icon"></div>
            </div>
        </div>
    </template>
</template>

<style lang="less" scoped>
@import './index.less';
</style>
