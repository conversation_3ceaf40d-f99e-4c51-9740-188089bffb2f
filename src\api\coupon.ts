import { request } from "@/common/request";
import { GoodsInfo, GroupKey } from "./goods";
import { URLCommon } from "@/common/env";
import { dateFormat } from "@/common/utils";

export interface CouponInfo {
    couponCode: string;
    canUse: boolean;
    used: boolean;
    expired: boolean;
    validEndTime: string;
    priceCent: number;
    goodsCouponData: {
        priceCent: number;
        desc: string;
        uniqKey: string;
        name: string
    }
}

export const getUserCoupons = async (data: { goodsDataType: GoodsInfo['dataType'], goodsDataCode: GoodsInfo['dataCode'] }) => {
    const res = await request({
        hostName: 'squirrel',
        url: 'api/web/coupon/list.htm',
        data: {
            tiku: URLCommon.tiku,
            ...data
        }
    });

    const coupons: CouponInfo[] = res.itemList.map((item: any) => ({
        couponUniqKey: item.couponTplCode,
        couponCode: item.couponCode,
        priceCent: item.discountValue * 100,
        goodsCouponData: {
            name: item.couponName,
            desc: item.description,
            priceCent: item.discountValue * 100,
            uniqKey: item.couponTplCode
        },
        validStartTime: item.activeStartTime,
        validEndTime: dateFormat(item.activeEndTime, 'yyyy-MM-dd'),
        discountType: item.discountType,
        canUse: item.canUse,
        expired: item.status === 3,
        used: item.status === 2
    }));
    const canUse: CouponInfo[] = [];
    const used: CouponInfo[] = [];
    const expired: CouponInfo[] = [];

    coupons.forEach(item => {

        if (item.canUse) {
            canUse.push(item);
        } else if (item.used) {
            used.push(item);
        } else if (item.expired) {
            expired.push(item);
        }
    });

    return {
        userCoupons: coupons,
        canUseCoupons: canUse,
        usedCoupons: used,
        expiredCoupons: expired
    };
};