import { createRouter, createWebHashHistory } from "vue-router";

function loadView(view: string) {
  return () => import(`../application/${view}/index.vue`);
}

const routes = [
  {
    name: 'login',
    path: '/login',
    component: loadView('login')
  },
  {
    name: 'default',
    path: `/`,
    redirect: () => {
      return {
        name: 'home'
      }
    }
  }
];

const match = location.pathname.match('.*\/([^\.]+)\.html');
const pagePathName = match ? match[1] : 'index';

routes.push({
  name: 'home',
  path: `/${pagePathName}`,
  component: loadView(pagePathName)
});

const router = createRouter({
  history: createWebHashHistory(),
  routes
});

export default router;
