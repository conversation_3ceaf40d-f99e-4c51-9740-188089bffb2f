import { URLParams } from './env';
import { MCProtocol, MCBaseUtils } from '@simplex/simple-base';

class Jump {
    whiteURLParams = {
        _system: true,
        _clv: true,
        _pkgName: true,
        _operator: true,
        _manufacturer: true,
        _renyuan: true,
        _systemVersion: true,
        _launch: true,
        _version: true,
        _network: true,
        _j: true,
        _product: true,
        _productCategory: true,
        _platform: true,
        _r: true,
        _appName: true,
        forOldPeople: true,
        bizVersion: true,
        schoolCode: true,
        from: true,
        fromPageCode: true,
        fromPathCode: true,
        pushCode: true,
        bizCode: true,
        schoolName: true,
        patternCode: true,
        sceneCode: true,
        kemuStyle: true,
        carStyle: true,
        authToken: true,
    }
    setWhiteURLParams(params) {
        this.whiteURLParams = params
    }
    openWeb(config: { url: string, title?: string, params?: object, [x: string]: any }) {
        config.url = config.url.trim();

        const urlObj = this.dealUrl(config.url);
        const baseUrl = urlObj.baseUrl;
        const hashParamsStr = urlObj.hashParamsStr;
        let urlParams = urlObj.urlParams;

        // 可能是协议，所以优先设置协议传过来的参数（因为是配置的），其次设置调用传过来的参数，最后是公共参数
        urlParams = {
            kemuStyle: URLParams.kemuStyle,
            carStyle: URLParams.carStyle,
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode,
            ...config.params,
            ...urlParams
        };

        const url = `${baseUrl}?${this.delRepeatUrlParams({ ...urlParams }, false)}${hashParamsStr}`;

        if (MCBaseUtils.isInApp) {
            MCProtocol.Core.Web.open({
                titleBar: config.titleBar || true,
                toolbar: config.toolbar || false,
                menu: config.menu || false,
                button: config.button || false,
                title: config.title || '',
                url
            });
        } else {
            window.open(url);
        }
    }
    // 并没有去兼容ios  buyWebView的版本，直接用最新的new-vip
    openVipWebView(config: {
        url: string;
        /** ios用 */
        style?: number;
        /** url参数 */
        urlParams?: object
        /** 协议 */
        protocolParams?: object
    }) {

        const { baseUrl,
            urlParams,
            hashParamsStr
        } = this.dealUrl(config.url);

        const url = `${baseUrl}?${this.delRepeatUrlParams({ ...urlParams, ...config.urlParams })}${hashParamsStr}`;

        if (!MCBaseUtils.isInApp) {
            window.open(url);
            return;
        }

        let paramsStr = '';
        if (config.protocolParams) {
            for (const key in config.protocolParams) {
                paramsStr += `&${key}=${config.protocolParams[key]}`;
            }
            paramsStr = paramsStr.replace(/^&(.*)/, '$1');
        }

        this.openWeb({
            url: `http://core.nav.wanmeixiangsu.cn/vip/new-vip?${paramsStr}&page=${encodeURIComponent(url)}`
        });
    }
    delRepeatUrlParams(params = {}, addUrlParams = true) {
        const whiteURLParams = this.whiteURLParams;
        let urlParams = {};
        for (const key in URLParams) {
            if (whiteURLParams[key]) {
                urlParams[key] = URLParams[key];
            }
        }
        if (addUrlParams) {
            urlParams = {
                ...urlParams,
                ...params
            }
        } else {
            urlParams = {
                ...params
            };
        }

        let str = '';
        for (const key in urlParams) {
            if (urlParams[key]) {
                str += `${key}=${encodeURIComponent(urlParams[key])}&`;
            }
        }

        return str.replace(/[&]$/, '');
    }
    dealUrl(url) {
        // // 解决相对路径的跳转
        // if ((/^\.\/([^/]+)\.html/).test(url)) {

        //     const goName = RegExp.$1;

        //     url = location.origin + location.pathname.replace(/\/[^/]+(\.html)/g, `/${goName}.html`) +
        //         `${url.includes('#') ? '#' + url.split('#')[1] : ''}`;

        // }
        let urlObj;
        if ((/^\.\/([^/]+)/).test(url)) {
            urlObj = new URL(url, location.href);
        } else {
            urlObj = new URL(url);
        }

        const urlParams = {};

        const baseUrl = urlObj.origin + urlObj.pathname;

        if (urlObj.search) {
            const urlSearch = urlObj.search.split('?')[1];

            urlSearch.split('&').forEach(item => {
                const [key, value] = item.split('=');
                urlParams[decodeURIComponent(key)] = decodeURIComponent(value);
            });
        }

        return {
            baseUrl,
            urlParams,
            hashParamsStr: urlObj.hash
        };
    }
    navigateTo(url, params = {}) {
        const { baseUrl,
            urlParams,
            hashParamsStr
        } = this.dealUrl(url);

        location.href = `${baseUrl}?${this.delRepeatUrlParams({ ...urlParams, ...params })}${hashParamsStr}`;
        throw new Error('不再继续往下走打点');
    }
    replace(url, params = {}) {
        const { baseUrl,
            urlParams,
            hashParamsStr
        } = this.dealUrl(url);

        location.replace(`${baseUrl}?${this.delRepeatUrlParams({ ...urlParams, ...params })}${hashParamsStr}`);
        throw new Error('不再继续往下走打点');
    }
    reload() {
        setTimeout(() => {
            window.location.reload();
            throw new Error('不再继续往下走打点');
        }, 200);
    }
}

export default new Jump();