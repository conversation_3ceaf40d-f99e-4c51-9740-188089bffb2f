<script setup lang="ts">
const props = defineProps(['showClose', 'titleText', 'contentTxt', 'cancleText', 'okText', 'opacity', 'maskClose']);

const emit = defineEmits<{
    (e: 'Ok', flag: boolean): void;
    (e: 'Close', flag: false | null): void;
}>();

const onClose = (flag: boolean | null) => {
    if (flag) {
        emit('Ok', true);
    } else {
        emit('Close', flag);
    }
};

const onDelegateClick = (e: Event) => {
    const dataType = e.target?.getAttribute('data-type');

    if (dataType === 'mask' && props.maskClose) {
        onClose(null);
        return;
    }

    if (dataType === 'close') {
        onClose(null);
    } else if (dataType === 'cancel') {
        onClose(false);
    } else if (dataType === 'confirm') {
        onClose(true);
    }
};
</script>

<template>
    <div class="modal" :style="{ 'background-color': `rgba(0, 0, 0, ${props.opacity || 0.5})` }" data-type="mask" @click="onDelegateClick">
        <div class="modal-content">
            <div v-if="props.showClose" class="close" data-type="close"></div>

            <div class="title">{{ props.titleText || '敬请确认' }}</div>

            <div class="content-body">
                <slot>
                    {{ props.contentTxt }}
                </slot>
            </div>

            <div class="footer">
                <div v-if="props.cancleText" data-type="cancel" class="footer-active cancle">{{ props.cancleText }}</div>

                <div data-type="confirm" class="footer-active ok">{{ props.okText || '同意并继续' }}</div>
            </div>
        </div>
    </div>
</template>

<style lang="less" scoped>
@import './index.less';
</style>
