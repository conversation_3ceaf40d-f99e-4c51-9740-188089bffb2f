.modal {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;

    .pay-content {
        padding: 25px 15px 15px 15px;
        background: #fff;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;

        &.fullScreen {
            height: 100vh;
        }
    }

    .pay-hd {
        text-align: center;
        position: relative;
        padding-bottom: 15px;

        .close-icon {
            width: 30px;
            height: 30px;
            position: absolute;
            left: 0;
            z-index: 1;
            background: url(./images/close.png) no-repeat left top;
            background-size: 16px 16px;
        }

        .pay-title {
            font-size: 18px;
            color: #333333;
            letter-spacing: 0;
        }

        .pay-dec{
            font-size: 15px;
            color: #333;
            line-height: 21px;
            text-align: center;
            margin-top: 14px;
        }
    }

    .pay-ft {
        padding: 20px 0 0 0;

        .pay-confirm {
            height: 44px;
            line-height: 44px;
            text-align: center;
            background: #0DC8FF;
            border-radius: 22px;
            font-size: 18px;
            margin-top: 25px;
            color: white;
            letter-spacing: 0;
            position: relative;

            .tips {
                padding: 0 10px;
                height: 22px;
                background: linear-gradient(90deg, #ff7810, #fe3c29 55%, #fe6164);
                border-radius: 33px 33px 33px 2px;
                position: absolute;
                right: 10px;
                top: 0;
                transform: translateY(-80%);
                font-size: 12px;
                color: white;
                display: flex;
                align-items: center;
            }
        }
    }
}