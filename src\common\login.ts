/*
 * ------------------------------------------------------------------
 * 登录相关
 * ------------------------------------------------------------------
 */
import { getAuthToken, getUserInfo } from '@/common/core';
import { Platform } from '@/common/env';
import { promisify } from '@/common/utils';
import Jump from '@/common/navigate';
import { MCProtocol } from '@simplex/simple-base';
import { ListenLogin, LoginType } from './broadcast/login';
import userStore from '@/store/modules/user';
import router from '@/router/index'

export const login = async (reload = true) => {
    if (Platform.isInApp) {
        const info = await promisify(MCProtocol.Core.User.login)({
            from: 'ji<PERSON>ob<PERSON><PERSON>',
            skipAuthRealName: true,
            pageType: 'quicklogin'
        });

        if (info.success) {
            const userInfoStore = userStore();

            const userInfo = await getUserInfo();

            userInfoStore.setUserInfo(userInfo);

            if (reload) {
                Jump.reload();
                return '';
            }
            return await getAuthToken();
        }

        throw new Error('登录失败');

    } else {
        router.push({
            name: 'login'
        });

        await new Promise(resolve => {
            ListenLogin(({ type, data }: { type: string, data: string }) => {
                if (type === LoginType.success) {
                    resolve(data)
                }
            }, true)
        })
    }

    if (reload) {
        Jump.reload();
        return '';
    }

    return await getAuthToken();
};

export const checkedLogin = async (reload = true) => {
    const authToken = await getAuthToken();

    if (!authToken) {
        return await login(reload);
    }

    return authToken;
};